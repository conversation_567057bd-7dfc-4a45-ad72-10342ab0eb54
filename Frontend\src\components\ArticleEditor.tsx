'use client';

import { useRef, useState, useEffect } from 'react';

interface ArticleEditorProps {
  initialContent?: string;
  onChange?: (content: string) => void;
  onSave?: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
}

export default function ArticleEditor({
  initialContent = '',
  onChange,
  onSave,
  placeholder = 'Start writing your article...',
  height = 500,
  disabled = false
}: ArticleEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [content, setContent] = useState(initialContent);
  const [isLoading, setIsLoading] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (editorRef.current && initialContent) {
      editorRef.current.innerHTML = initialContent;
      updateWordCount(initialContent);
    }
  }, [initialContent]);

  const updateWordCount = (text: string) => {
    const plainText = text.replace(/<[^>]*>/g, '');
    const words = plainText.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
  };

  const handleInput = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      updateWordCount(newContent);
      onChange?.(newContent);
    }
  };

  const handleSave = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onSave?.(content);
    }
  };

  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleInput();
  };

  const insertImage = () => {
    const url = prompt('Enter image URL:');
    if (url) {
      formatText('insertHTML', `<img src="${url}" alt="Image" style="max-width: 100%; height: auto; border-radius: 8px; margin: 1em 0;" />`);
    }
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      formatText('createLink', url);
    }
  };

  return (
    <div className="w-full">
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between mb-2 p-3 bg-gray-50 rounded-t-lg border border-b-0">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            Words: <span className="font-medium">{wordCount}</span>
          </span>
          <span className="text-sm text-gray-600">
            Reading time: <span className="font-medium">{Math.ceil(wordCount / 200)} min</span>
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {onSave && (
            <button
              onClick={handleSave}
              disabled={disabled || isLoading}
              className="px-4 py-1.5 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </button>
          )}
        </div>
      </div>

      {/* 格式化工具栏 */}
      <div className="flex flex-wrap items-center gap-1 p-2 bg-white border-l border-r border-gray-300">
        <button
          type="button"
          onClick={() => formatText('bold')}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Bold"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 5a1 1 0 011-1h5.5a2.5 2.5 0 010 5H4v2h4.5a2.5 2.5 0 010 5H4a1 1 0 01-1-1V5z"/>
          </svg>
        </button>

        <button
          type="button"
          onClick={() => formatText('italic')}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Italic"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 5a1 1 0 000 2h1.5L7.5 13H6a1 1 0 000 2h4a1 1 0 000-2h-1.5L10.5 7H12a1 1 0 000-2H8z"/>
          </svg>
        </button>

        <button
          type="button"
          onClick={() => formatText('underline')}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Underline"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 18h12a1 1 0 000-2H4a1 1 0 000 2zM6 4a1 1 0 011-1h6a1 1 0 011 1v6a3 3 0 01-6 0V4z"/>
          </svg>
        </button>

        <div className="w-px h-6 bg-gray-300 mx-1"></div>

        <button
          type="button"
          onClick={() => formatText('formatBlock', 'h2')}
          className="px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
          title="Heading 2"
        >
          H2
        </button>

        <button
          type="button"
          onClick={() => formatText('formatBlock', 'h3')}
          className="px-2 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
          title="Heading 3"
        >
          H3
        </button>

        <div className="w-px h-6 bg-gray-300 mx-1"></div>

        <button
          type="button"
          onClick={() => formatText('insertUnorderedList')}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Bullet List"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
          </svg>
        </button>

        <button
          type="button"
          onClick={() => formatText('insertOrderedList')}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Numbered List"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
          </svg>
        </button>

        <div className="w-px h-6 bg-gray-300 mx-1"></div>

        <button
          type="button"
          onClick={insertLink}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Insert Link"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z"/>
          </svg>
        </button>

        <button
          type="button"
          onClick={insertImage}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded"
          title="Insert Image"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
          </svg>
        </button>
      </div>

      {/* 编辑器内容区域 */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`min-h-[${height}px] p-4 border-l border-r border-b border-gray-300 rounded-b-lg bg-white focus:outline-none ${
          isFocused ? 'ring-2 ring-blue-500 ring-opacity-20' : ''
        } ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}`}
        style={{
          minHeight: `${height}px`,
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          fontSize: '16px',
          lineHeight: '1.6',
          color: '#374151'
        }}
        suppressContentEditableWarning={true}
      >
        {!content && (
          <div className="text-gray-400 pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* 编辑器底部信息 */}
      <div className="mt-2 text-xs text-gray-500 flex justify-between">
        <span>Use Ctrl+B for bold • Ctrl+I for italic • Ctrl+S to save</span>
        <span>Rich Text Editor</span>
      </div>
    </div>
  );
}
