const { sequelize } = require('../models');

async function migrateDatabase() {
  try {
    console.log('🔧 Starting database migration...');

    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // 同步所有模型
    await sequelize.sync({ alter: true });
    console.log('✅ Database models synchronized successfully.');

    // 显示数据库信息
    const dbName = sequelize.config.database;
    const dbHost = sequelize.config.host;
    const dbDialect = sequelize.config.dialect;
    
    console.log(`📊 Database Info:`);
    console.log(`   - Name: ${dbName}`);
    console.log(`   - Host: ${dbHost}`);
    console.log(`   - Dialect: ${dbDialect}`);

    // 检查表是否存在
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log(`📋 Tables in database (${tables.length}):`);
    tables.forEach(table => {
      console.log(`   - ${table}`);
    });

    console.log('🎉 Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateDatabase().then(() => {
    console.log('Migration script completed.');
    process.exit(0);
  }).catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
}

module.exports = migrateDatabase;
