'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import ThumbnailSelector from '@/components/ThumbnailSelector';

export default function CreatePage() {
  const router = useRouter();
  const [contentType, setContentType] = useState<'article' | 'video' | 'audio'>('article');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    mediaFile: null as File | null,
    thumbnail: null as File | null,
    videoQuality: '1080p' as '480p' | '720p' | '1080p' | '1440p' | '2160p' | '4320p' | '8K',
    audioBitrate: '320' as '128' | '192' | '256' | '320',
    autoThumbnail: '' // 自动生成的缩略图
  });
  const [videoInfo, setVideoInfo] = useState<{
    duration: number;
    width: number;
    height: number;
    size: number;
  } | null>(null);
  const [showThumbnailSelector, setShowThumbnailSelector] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [aiReviewResult, setAiReviewResult] = useState<{
    passed: boolean;
    issues: string[];
    suggestions: string[];
  } | null>(null);
  const [showAiReview, setShowAiReview] = useState(false);

  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'finance', label: 'Finance' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'travel', label: 'Travel' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'health', label: 'Health' },
    { value: 'science', label: 'Science' },
    { value: 'business', label: 'Business' }
  ];

  const trendingTopics = [
    { title: 'AI Revolution in 2024', category: 'Technology', views: '2.3K', trend: '+15%' },
    { title: 'Sustainable Living Tips', category: 'Lifestyle', views: '1.8K', trend: '+22%' },
    { title: 'Crypto Market Analysis', category: 'Finance', views: '3.1K', trend: '+8%' },
    { title: 'Remote Work Culture', category: 'Business', views: '1.5K', trend: '+12%' },
    { title: 'Healthy Recipe Ideas', category: 'Food', views: '2.7K', trend: '+18%' }
  ];

  // AI审核系统
  const runAiContentReview = async (content: string, title: string) => {
    const sensitiveKeywords = [
      '政治敏感', '血腥暴力', '虚假信息', '仇恨言论', '色情内容',
      '诈骗', '毒品', '武器', '恐怖主义', '种族歧视'
    ];

    const issues: string[] = [];
    const suggestions: string[] = [];

    // 模拟AI检测
    const fullText = `${title} ${content}`.toLowerCase();

    if (fullText.includes('政治') || fullText.includes('政府')) {
      issues.push('检测到政治敏感内容');
      suggestions.push('请避免涉及政治话题，专注于技术或生活内容');
    }

    if (fullText.includes('血腥') || fullText.includes('暴力')) {
      issues.push('检测到暴力血腥内容');
      suggestions.push('请使用更温和的表达方式');
    }

    if (fullText.includes('假') || fullText.includes('虚假')) {
      issues.push('可能包含虚假信息');
      suggestions.push('请确保内容真实可靠，提供可信来源');
    }

    // 内容质量检查
    if (content.length < 100) {
      suggestions.push('建议增加内容长度，提供更详细的信息');
    }

    if (!title.trim()) {
      issues.push('标题不能为空');
    }

    return {
      passed: issues.length === 0,
      issues,
      suggestions
    };
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAiReview = async () => {
    setShowAiReview(true);
    const result = await runAiContentReview(formData.content, formData.title);
    setAiReviewResult(result);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 先进行AI审核
      const reviewResult = await runAiContentReview(formData.content, formData.title);

      if (!reviewResult.passed) {
        setAiReviewResult(reviewResult);
        setShowAiReview(true);
        setIsSubmitting(false);
        return;
      }

      // 模拟提交
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Content submitted:', { ...formData, contentType });
      router.push('/');
    } catch (error) {
      console.error('Error submitting content:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = (field: 'mediaFile' | 'thumbnail', file: File) => {
    setFormData(prev => ({ ...prev, [field]: file }));

    // 如果是视频文件，显示缩略图选择器
    if (field === 'mediaFile' && contentType === 'video') {
      setShowThumbnailSelector(true);
    }
  };

  const handleThumbnailSelect = (thumbnail: string) => {
    setFormData(prev => ({ ...prev, autoThumbnail: thumbnail }));
  };

  const handleVideoInfo = (info: { duration: number; width: number; height: number; size: number }) => {
    setVideoInfo(info);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex max-w-7xl mx-auto px-6 py-8 gap-8">
        {/* Main Content */}
        <main className="flex-1">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Create New Content
            </h1>
            <p className="text-lg text-gray-600">
              Share your thoughts, insights, and stories with the world.
            </p>
          </div>

          {/* Content Type Selection */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
              <button
                onClick={() => setContentType('article')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  contentType === 'article'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                📝 Article
              </button>
              <button
                onClick={() => setContentType('video')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  contentType === 'video'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                🎥 Video
              </button>
              <button
                onClick={() => setContentType('audio')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  contentType === 'audio'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                🎵 Audio
              </button>
            </div>
          </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {contentType === 'article' ? 'Article' : contentType === 'video' ? 'Video' : 'Audio'} Title *
              </label>
              <input
                type="text"
                placeholder={`Enter an engaging title for your ${contentType}...`}
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            {/* Media Upload for Video/Audio */}
            {(contentType === 'video' || contentType === 'audio') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {contentType === 'video' ? 'Video File' : 'Audio File'} *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <input
                    type="file"
                    accept={contentType === 'video' ? 'video/*' : 'audio/*'}
                    onChange={(e) => e.target.files?.[0] && handleFileUpload('mediaFile', e.target.files[0])}
                    className="hidden"
                    id="media-upload"
                  />
                  <label htmlFor="media-upload" className="cursor-pointer">
                    <div className="text-4xl mb-2">{contentType === 'video' ? '🎥' : '🎵'}</div>
                    <p className="text-gray-600">
                      Click to upload {contentType} file
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {contentType === 'video' ? 'MP4, AVI, MOV up to 100MB' : 'MP3, WAV, AAC up to 50MB'}
                    </p>
                  </label>
                  {formData.mediaFile && (
                    <p className="mt-2 text-sm text-green-600">
                      ✓ {formData.mediaFile.name}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Video Quality Settings */}
            {contentType === 'video' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Video Quality
                  </label>
                  <select
                    value={formData.videoQuality}
                    onChange={(e) => handleInputChange('videoQuality', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="480p">480p (Standard Definition)</option>
                    <option value="720p">720p (HD)</option>
                    <option value="1080p">1080p (Full HD)</option>
                    <option value="1440p">1440p (2K)</option>
                    <option value="2160p">2160p (4K)</option>
                    <option value="4320p">4320p (8K)</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Higher quality means larger file size and longer upload time
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thumbnail Image
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => e.target.files?.[0] && handleFileUpload('thumbnail', e.target.files[0])}
                      className="hidden"
                      id="thumbnail-upload"
                    />
                    <label htmlFor="thumbnail-upload" className="cursor-pointer">
                      <div className="text-2xl mb-1">🖼️</div>
                      <p className="text-sm text-gray-600">Upload thumbnail</p>
                    </label>
                    {formData.thumbnail && (
                      <p className="mt-1 text-xs text-green-600">
                        ✓ {formData.thumbnail.name}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Video Thumbnail Selector */}
            {contentType === 'video' && formData.mediaFile && showThumbnailSelector && (
              <div className="bg-gray-50 rounded-lg p-6">
                <ThumbnailSelector
                  videoFile={formData.mediaFile}
                  onThumbnailSelect={handleThumbnailSelect}
                  onVideoInfo={handleVideoInfo}
                />
              </div>
            )}

            {/* Audio Quality Settings */}
            {contentType === 'audio' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Audio Bitrate
                </label>
                <select
                  value={formData.audioBitrate}
                  onChange={(e) => handleInputChange('audioBitrate', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="128">128 kbps (Standard)</option>
                  <option value="192">192 kbps (Good)</option>
                  <option value="256">256 kbps (High)</option>
                  <option value="320">320 kbps (Premium)</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Higher bitrate provides better audio quality but larger file size
                </p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                placeholder="Write a compelling description..."
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-24 resize-none"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags
                </label>
                <input
                  type="text"
                  placeholder="Enter tags separated by commas"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <textarea
                placeholder="Write your article content here..."
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[400px] resize-none"
                required
              />
            </div>

            {/* AI Review Results */}
            {showAiReview && aiReviewResult && (
              <div className={`p-4 rounded-lg border ${
                aiReviewResult.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              }`}>
                <h4 className={`font-medium mb-2 ${
                  aiReviewResult.passed ? 'text-green-800' : 'text-red-800'
                }`}>
                  🤖 AI Content Review {aiReviewResult.passed ? 'Passed' : 'Failed'}
                </h4>

                {aiReviewResult.issues.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm font-medium text-red-700 mb-1">Issues Found:</p>
                    <ul className="text-sm text-red-600 list-disc list-inside">
                      {aiReviewResult.issues.map((issue, index) => (
                        <li key={index}>{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {aiReviewResult.suggestions.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-blue-700 mb-1">Suggestions:</p>
                    <ul className="text-sm text-blue-600 list-disc list-inside">
                      {aiReviewResult.suggestions.map((suggestion, index) => (
                        <li key={index}>{suggestion}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
              <div className="flex gap-3 flex-1">
                <button
                  type="button"
                  onClick={handleAiReview}
                  className="px-4 py-2 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  🤖 AI Review
                </button>

                <button
                  type="button"
                  onClick={() => localStorage.setItem('content-draft', JSON.stringify(formData))}
                  className="px-4 py-2 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Save Draft
                </button>

                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900 transition-colors"
                >
                  Cancel
                </button>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || !formData.title || !formData.content}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Publishing...' : `Publish ${contentType.charAt(0).toUpperCase() + contentType.slice(1)}`}
              </button>
            </div>
          </form>
        </div>
        </main>

        {/* Sidebar - Trending Topics */}
        <aside className="w-80">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              🔥 Trending Topics
            </h3>
            <div className="space-y-4">
              {trendingTopics.map((topic, index) => (
                <div key={index} className="p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                  <h4 className="font-medium text-gray-900 text-sm mb-1">
                    {topic.title}
                  </h4>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="px-2 py-1 bg-gray-100 rounded">
                      {topic.category}
                    </span>
                    <div className="flex items-center space-x-2">
                      <span>{topic.views} views</span>
                      <span className="text-green-600 font-medium">{topic.trend}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Content Tips */}
          <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              💡 Content Tips
            </h3>
            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-start space-x-2">
                <span className="text-blue-500">•</span>
                <p>Use engaging titles that capture attention</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500">•</span>
                <p>Add relevant tags to improve discoverability</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500">•</span>
                <p>Include high-quality images or media</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-blue-500">•</span>
                <p>Write clear and compelling descriptions</p>
              </div>
              <div className="flex items-start space-x-2">
                <span className="text-red-500">•</span>
                <p>Avoid sensitive political or violent content</p>
              </div>
            </div>
          </div>

          {/* AI Review Info */}
          <div className="mt-6 bg-blue-50 rounded-lg border border-blue-200 p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              🤖 AI Content Review
            </h3>
            <p className="text-sm text-blue-700 mb-3">
              Our AI system automatically reviews content for:
            </p>
            <ul className="text-xs text-blue-600 space-y-1">
              <li>• Political sensitivity</li>
              <li>• Violence and graphic content</li>
              <li>• Misinformation and fake news</li>
              <li>• Hate speech and discrimination</li>
              <li>• Content quality and relevance</li>
            </ul>
          </div>
        </aside>
      </div>
    </div>
  );
}
