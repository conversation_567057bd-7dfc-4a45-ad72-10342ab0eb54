'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Header from '@/components/Header';
import RichTextEditor from '@/components/RichTextEditor';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeftIcon,
  EyeIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

interface DraftData {
  id: number;
  title: string;
  content: string;
  contentHtml: string;
  excerpt: string;
  category: string;
  tags: string[];
  featuredImage: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

const categories = [
  'technology',
  'business', 
  'health',
  'sports',
  'entertainment',
  'politics',
  'science',
  'lifestyle'
];

export default function EditDraftPage() {
  const { user, token } = useAuth();
  const router = useRouter();
  const params = useParams();
  const draftId = params.id as string;
  
  const [draft, setDraft] = useState<DraftData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [previewMode, setPreviewMode] = useState(false);
  const [tagInput, setTagInput] = useState('');
  const [error, setError] = useState<string | null>(null);
  const autoSaveRef = useRef<NodeJS.Timeout | null>(null);

  const autoSave = useCallback(async () => {
    if (!draft || !token) return;

    try {
      setSaveStatus('saving');

      const response = await fetch(`http://localhost:5000/api/drafts/${draft.id}/autosave`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: draft.title,
          content: draft.content,
          contentHtml: draft.contentHtml
        })
      });

      if (response.ok) {
        setSaveStatus('saved');
        setTimeout(() => setSaveStatus('idle'), 2000);
      } else {
        setSaveStatus('error');
      }
    } catch (error) {
      console.error('Auto-save error:', error);
      setSaveStatus('error');
    }
  }, [draft, token]);

  const fetchDraft = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`http://localhost:5000/api/drafts/${draftId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDraft(data.data.draft);
      } else if (response.status === 404) {
        setError('Draft not found');
      } else {
        setError('Failed to load draft');
      }
    } catch (error) {
      console.error('Error fetching draft:', error);
      setError('Failed to load draft');
    } finally {
      setLoading(false);
    }
  }, [draftId, token]);

  useEffect(() => {
    if (!user || !token) {
      router.push('/auth/login');
      return;
    }

    fetchDraft();
  }, [user, token, router, draftId, fetchDraft]);

  // Auto-save functionality
  useEffect(() => {
    if (draft && (draft.title || draft.content)) {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }

      autoSaveRef.current = setTimeout(() => {
        autoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity
    }

    return () => {
      if (autoSaveRef.current) {
        clearTimeout(autoSaveRef.current);
      }
    };
  }, [draft, autoSave]);



  const saveDraft = async (status: 'draft' | 'ready_for_review' = 'draft') => {
    if (!draft || !token) return;

    try {
      setSaving(true);
      setSaveStatus('saving');

      const draftData = {
        ...draft,
        status
      };

      const response = await fetch(`http://localhost:5000/api/drafts/${draft.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(draftData)
      });

      if (response.ok) {
        const result = await response.json();
        setDraft(result.data.draft);
        setSaveStatus('saved');
        
        if (status === 'ready_for_review') {
          router.push('/content');
        }
      } else {
        setSaveStatus('error');
      }
    } catch (error) {
      console.error('Save error:', error);
      setSaveStatus('error');
    } finally {
      setSaving(false);
    }
  };

  const deleteDraft = async () => {
    if (!draft || !token) return;
    
    if (!confirm('Are you sure you want to delete this draft? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/drafts/${draft.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        router.push('/content');
      } else {
        alert('Failed to delete draft');
      }
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete draft');
    }
  };

  const handleTagAdd = () => {
    if (!draft) return;
    
    if (tagInput.trim() && !draft.tags.includes(tagInput.trim()) && draft.tags.length < 10) {
      setDraft(prev => prev ? ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }) : null);
      setTagInput('');
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    if (!draft) return;
    
    setDraft(prev => prev ? ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }) : null);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleTagAdd();
    }
  };

  const getSaveStatusIcon = () => {
    switch (saveStatus) {
      case 'saving':
        return <CloudArrowUpIcon className="w-5 h-5 text-blue-600 animate-pulse" />;
      case 'saved':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error || !draft) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
            <p className="text-red-600">{error || 'Draft not found'}</p>
            <Link
              href="/content"
              className="mt-4 inline-block bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Back to Content
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/content"
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </Link>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Draft</h1>
              <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  {getSaveStatusIcon()}
                  <span>
                    {saveStatus === 'saving' && 'Saving...'}
                    {saveStatus === 'saved' && 'Saved'}
                    {saveStatus === 'error' && 'Save failed'}
                    {saveStatus === 'idle' && 'Ready'}
                  </span>
                </div>
                <span>•</span>
                <span>Created {formatDate(draft.createdAt)}</span>
                <span>•</span>
                <span>Updated {formatDate(draft.updatedAt)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setPreviewMode(!previewMode)}
              className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <EyeIcon className="w-5 h-5 mr-2" />
              {previewMode ? 'Edit' : 'Preview'}
            </button>
            
            <button
              onClick={() => saveDraft('draft')}
              disabled={saving}
              className="px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
            >
              Save Draft
            </button>
            
            <button
              onClick={() => saveDraft('ready_for_review')}
              disabled={saving || !draft.title.trim() || !draft.content.trim()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Submit for Review
            </button>
            
            <button
              onClick={deleteDraft}
              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete draft"
            >
              <TrashIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          {!previewMode ? (
            /* Edit Mode */
            <div className="p-6 space-y-6">
              {/* Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  type="text"
                  value={draft.title}
                  onChange={(e) => setDraft(prev => prev ? ({ ...prev, title: e.target.value }) : null)}
                  placeholder="Enter your article title..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                />
              </div>

              {/* Category and Tags */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={draft.category}
                    onChange={(e) => setDraft(prev => prev ? ({ ...prev, category: e.target.value }) : null)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags ({draft.tags.length}/10)
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleKeyPress}
                      placeholder="Add a tag..."
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <button
                      onClick={handleTagAdd}
                      disabled={!tagInput.trim() || draft.tags.length >= 10}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Add
                    </button>
                  </div>
                  
                  {draft.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {draft.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                        >
                          {tag}
                          <button
                            onClick={() => handleTagRemove(tag)}
                            className="ml-2 text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Content */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content *
                </label>
                <RichTextEditor
                  value={draft.content}
                  onChange={(content, html) => setDraft(prev => prev ? ({ 
                    ...prev, 
                    content, 
                    contentHtml: html 
                  }) : null)}
                  placeholder="Start writing your article..."
                  className="w-full"
                />
              </div>

              {/* Excerpt */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Excerpt
                </label>
                <textarea
                  value={draft.excerpt}
                  onChange={(e) => setDraft(prev => prev ? ({ ...prev, excerpt: e.target.value }) : null)}
                  placeholder="Brief description of your article..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Featured Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Featured Image URL
                </label>
                <input
                  type="url"
                  value={draft.featuredImage}
                  onChange={(e) => setDraft(prev => prev ? ({ ...prev, featuredImage: e.target.value }) : null)}
                  placeholder="https://example.com/image.jpg"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          ) : (
            /* Preview Mode */
            <div className="p-6">
              <div className="prose max-w-none">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  {draft.title || 'Untitled'}
                </h1>
                
                {draft.featuredImage && (
                  <div className="relative w-full h-64 mb-6">
                    <Image
                      src={draft.featuredImage}
                      alt="Featured"
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                )}
                
                {draft.excerpt && (
                  <p className="text-lg text-gray-600 italic mb-6">
                    {draft.excerpt}
                  </p>
                )}
                
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: draft.contentHtml || draft.content }}
                />
                
                {draft.tags.length > 0 && (
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex flex-wrap gap-2">
                      {draft.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
