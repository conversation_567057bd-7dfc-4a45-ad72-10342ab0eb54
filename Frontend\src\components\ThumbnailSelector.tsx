'use client';

import { useState, useEffect } from 'react';
import { generateMultipleThumbnails, getVideoInfo, formatDuration, formatFileSize, getQualityLabel } from '@/utils/videoThumbnail';
import { CheckIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

interface ThumbnailSelectorProps {
  videoFile: File;
  onThumbnailSelect: (thumbnail: string) => void;
  onVideoInfo?: (info: { duration: number; width: number; height: number; size: number }) => void;
}

export default function ThumbnailSelector({ 
  videoFile, 
  onThumbnailSelect,
  onVideoInfo 
}: ThumbnailSelectorProps) {
  const [thumbnails, setThumbnails] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [videoInfo, setVideoInfo] = useState<{
    duration: number;
    width: number;
    height: number;
    size: number;
  } | null>(null);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (videoFile) {
      generateThumbnails();
      loadVideoInfo();
    }
  }, [videoFile]);

  const generateThumbnails = async () => {
    try {
      setLoading(true);
      setError('');
      
      const thumbnailList = await generateMultipleThumbnails(videoFile, 5, {
        width: 320,
        height: 180,
        quality: 0.8
      });
      
      setThumbnails(thumbnailList);
      
      // 默认选择第一个缩略图
      if (thumbnailList.length > 0) {
        setSelectedIndex(0);
        onThumbnailSelect(thumbnailList[0]);
      }
    } catch (error) {
      console.error('Error generating thumbnails:', error);
      setError('Failed to generate thumbnails. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadVideoInfo = async () => {
    try {
      const info = await getVideoInfo(videoFile);
      setVideoInfo(info);
      onVideoInfo?.(info);
    } catch (error) {
      console.error('Error loading video info:', error);
    }
  };

  const handleThumbnailSelect = (index: number) => {
    setSelectedIndex(index);
    onThumbnailSelect(thumbnails[index]);
  };

  const handleRegenerate = () => {
    generateThumbnails();
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Generating Thumbnails...</h3>
          <ArrowPathIcon className="w-5 h-5 text-blue-600 animate-spin" />
        </div>
        
        <div className="grid grid-cols-5 gap-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="aspect-video bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Thumbnail Selection</h3>
          <button
            onClick={handleRegenerate}
            className="flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowPathIcon className="w-4 h-4" />
            Retry
          </button>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Choose Thumbnail</h3>
        <button
          onClick={handleRegenerate}
          className="flex items-center gap-2 text-blue-600 hover:text-blue-700 transition-colors text-sm"
        >
          <ArrowPathIcon className="w-4 h-4" />
          Regenerate
        </button>
      </div>

      {/* Video Info */}
      {videoInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Duration:</span>
              <div className="font-medium">{formatDuration(videoInfo.duration)}</div>
            </div>
            <div>
              <span className="text-gray-500">Resolution:</span>
              <div className="font-medium">
                {videoInfo.width}×{videoInfo.height} ({getQualityLabel(videoInfo.width, videoInfo.height)})
              </div>
            </div>
            <div>
              <span className="text-gray-500">File Size:</span>
              <div className="font-medium">{formatFileSize(videoInfo.size)}</div>
            </div>
            <div>
              <span className="text-gray-500">Format:</span>
              <div className="font-medium">{videoFile.type.split('/')[1].toUpperCase()}</div>
            </div>
          </div>
        </div>
      )}

      {/* Thumbnail Grid */}
      <div className="grid grid-cols-5 gap-3">
        {thumbnails.map((thumbnail, index) => (
          <button
            key={index}
            onClick={() => handleThumbnailSelect(index)}
            className={`relative aspect-video rounded-lg overflow-hidden border-2 transition-all duration-200 ${
              selectedIndex === index
                ? 'border-blue-500 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <img
              src={thumbnail}
              alt={`Thumbnail ${index + 1}`}
              className="w-full h-full object-cover"
            />
            
            {/* Selection Indicator */}
            {selectedIndex === index && (
              <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                <div className="bg-blue-500 rounded-full p-1">
                  <CheckIcon className="w-4 h-4 text-white" />
                </div>
              </div>
            )}
            
            {/* Frame Number */}
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
              {index + 1}
            </div>
          </button>
        ))}
      </div>

      {/* Selected Thumbnail Preview */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Selected Thumbnail Preview:</h4>
        <div className="flex items-start gap-4">
          <div className="w-32 aspect-video rounded-lg overflow-hidden border border-gray-200">
            <img
              src={thumbnails[selectedIndex]}
              alt="Selected thumbnail"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 text-sm text-gray-600">
            <p>This thumbnail will be displayed when users see your video in feeds and search results.</p>
            <p className="mt-1">You can regenerate thumbnails or upload a custom image if needed.</p>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Tips for better thumbnails:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Choose frames with clear, engaging visuals</li>
          <li>• Avoid blurry or dark frames</li>
          <li>• Consider frames that represent your content well</li>
          <li>• You can also upload a custom thumbnail image</li>
        </ul>
      </div>
    </div>
  );
}
