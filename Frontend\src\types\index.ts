// 基础作品接口
export interface BaseWork {
  id: number;
  title: string;
  description?: string;
  excerpt?: string;
  category: 'Technology' | 'Travel' | 'Lifestyle' | 'Food' | 'Entertainment' | 'Finance' | 'History' | 'Trending' | string;
  author: {
    id: number;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
    isFollowing?: boolean;
  } | string;
  tags: string[];
  views: number;
  likes: number;
  comments?: number;
  shares?: number;
  bookmarks?: number;
  featured?: boolean;
  published: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 文章类型
export interface Article extends BaseWork {
  type: 'article';
  content: string;
  image?: string;
  readTime: number;
}

// 视频类型
export interface Video extends BaseWork {
  type: 'video';
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number; // 秒
  resolution: {
    width: number;
    height: number;
    quality: '480p' | '720p' | '1080p' | '1440p' | '2160p' | '4320p' | '8K';
  };
  fileSize: number; // bytes
}

// 音频类型
export interface Audio extends BaseWork {
  type: 'audio';
  audioUrl: string;
  coverUrl?: string;
  duration: number; // 秒
  fileSize: number; // bytes
}

// 统一的作品类型
export type Work = Article | Video | Audio;

export interface User {
  _id: string;
  username: string;
  email: string;
  avatar: string;
  role: 'user' | 'admin';
  preferences: {
    categories: string[];
  };
  bookmarks: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  name: string;
  count: number;
}

export interface Comment {
  id: number;
  articleId?: number;
  author: {
    id: number;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
  } | string;
  avatar?: string; // 保持向后兼容
  content: string;
  likes: number;
  parentId?: number;
  replies?: Comment[];
  createdAt: string;
  updatedAt: string;
}
