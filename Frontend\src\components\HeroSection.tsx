'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

interface HeroSectionProps {
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showStats?: boolean;
  backgroundType?: 'gradient' | 'particles' | 'waves';
}

export default function HeroSection({
  title = "Welcome to the Creator's Digital Universe",
  subtitle = "Discover amazing articles on technology, lifestyle, travel, food and more. Your trusted source for quality news and content.",
  showSearch = true,
  showStats = true,
  backgroundType = 'gradient'
}: HeroSectionProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const stats = [
    { label: 'Creators', value: '10K+', icon: '👥' },
    { label: 'Categories', value: '500+', icon: '📚' },
    { label: 'Readers', value: '1M+', icon: '👁️' },
    { label: 'Online', value: '24/7', icon: '🌐' }
  ];

  if (!mounted) {
    return (
      <div className="relative min-h-[80vh] flex items-center justify-center bg-gradient-primary">
        <div className="animate-pulse">
          <div className="h-12 bg-white/20 rounded-lg w-96 mb-4"></div>
          <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
      {/* Dynamic Background */}
      <div className="absolute inset-0 z-0">
        {backgroundType === 'gradient' && (
          <div className="absolute inset-0 bg-gradient-to-br from-primary-600 via-secondary-600 to-accent-600 animate-pulse">
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
        )}
        
        {backgroundType === 'particles' && (
          <div className="absolute inset-0 bg-gradient-primary">
            {/* Floating particles */}
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white/30 rounded-full animate-float"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${3 + Math.random() * 2}s`
                }}
              />
            ))}
          </div>
        )}

        {backgroundType === 'waves' && (
          <div className="absolute inset-0 bg-gradient-primary">
            <svg
              className="absolute bottom-0 w-full h-32 text-white/10"
              viewBox="0 0 1200 120"
              preserveAspectRatio="none"
            >
              <path
                d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
                opacity=".25"
                fill="currentColor"
              />
              <path
                d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
                opacity=".5"
                fill="currentColor"
              />
              <path
                d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
                fill="currentColor"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        {/* Main Title */}
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in-up font-heading">
          <span className="bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent">
            {title}
          </span>
        </h1>

        {/* Subtitle */}
        <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          {subtitle}
        </p>

        {/* Search Bar */}
        {showSearch && (
          <div className="max-w-2xl mx-auto mb-12 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <form onSubmit={handleSearch} className="relative">
              <Input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search articles, topics, or authors..."
                variant="glass"
                size="lg"
                fullWidth
                leftIcon={
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
                className="text-lg shadow-2xl"
              />
            </form>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
          <Button
            variant="glass"
            size="lg"
            effect="glow"
            onClick={() => router.push('/create')}
            className="text-white border-white/30 hover:bg-white/20"
          >
            Start Creating
          </Button>
          <Button
            variant="ghost"
            size="lg"
            onClick={() => router.push('/explore')}
            className="text-white hover:bg-white/10"
          >
            Explore Content
          </Button>
          <Button
            variant="ghost"
            size="lg"
            onClick={() => router.push('/social')}
            className="text-white hover:bg-white/10"
          >
            Join Community
          </Button>
        </div>

        {/* Stats */}
        {showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center group hover:scale-105 transition-transform duration-300"
              >
                <div className="text-4xl mb-2 group-hover:animate-bounce">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-white/80 text-sm uppercase tracking-wider">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg
          className="w-6 h-6 text-white/60"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 14l-7 7m0 0l-7-7m7 7V3"
          />
        </svg>
      </div>
    </section>
  );
}
