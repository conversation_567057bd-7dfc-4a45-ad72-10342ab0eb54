'use client';

import React, { forwardRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'glass' | 'outline' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'rounded' | 'circle' | 'square';
  loading?: boolean;
  icon: ReactNode;
  tooltip?: string;
}

const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    shape = 'rounded',
    loading = false,
    icon,
    tooltip,
    disabled,
    ...props
  }, ref) => {
    
    // 基础样式
    const baseStyles = [
      'inline-flex items-center justify-center',
      'font-medium transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'relative overflow-hidden'
    ];

    // 变体样式
    const variantStyles = {
      primary: [
        'bg-gradient-primary text-white',
        'hover:shadow-lg hover:shadow-primary/25',
        'hover:-translate-y-0.5',
        'focus:ring-primary-500'
      ],
      secondary: [
        'bg-surface-2 text-text-primary',
        'hover:bg-surface hover:shadow-lg',
        'hover:-translate-y-0.5',
        'focus:ring-primary-500',
        'border border-border'
      ],
      ghost: [
        'bg-transparent text-text-primary',
        'hover:bg-surface-2',
        'focus:ring-primary-500'
      ],
      glass: [
        'bg-white/10 backdrop-blur-md border border-white/20',
        'text-text-primary hover:bg-white/20',
        'hover:-translate-y-0.5',
        'focus:ring-primary-500'
      ],
      outline: [
        'border-2 border-primary bg-transparent text-primary',
        'hover:bg-primary hover:text-white',
        'hover:-translate-y-0.5',
        'focus:ring-primary-500'
      ],
      danger: [
        'bg-gradient-to-r from-red-500 to-red-600 text-white',
        'hover:from-red-600 hover:to-red-700',
        'hover:shadow-lg hover:shadow-red-500/25',
        'hover:-translate-y-0.5',
        'focus:ring-red-500'
      ]
    };

    // 尺寸样式
    const sizeStyles = {
      xs: 'w-6 h-6 text-xs',
      sm: 'w-8 h-8 text-sm',
      md: 'w-10 h-10 text-base',
      lg: 'w-12 h-12 text-lg',
      xl: 'w-14 h-14 text-xl'
    };

    // 形状样式
    const shapeStyles = {
      rounded: 'rounded-lg',
      circle: 'rounded-full',
      square: 'rounded-none'
    };

    const buttonClasses = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      shapeStyles[shape],
      className
    );

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        title={tooltip}
        {...props}
      >
        {loading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
        ) : (
          icon
        )}
      </button>
    );
  }
);

IconButton.displayName = 'IconButton';

export { IconButton };
