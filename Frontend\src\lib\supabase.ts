import { createClient } from '@supabase/supabase-js'
import { mockSupabase } from './mockAuth'

// 开发模式开关 - 设置为 true 使用模拟认证，false 使用真实 Supabase
const USE_MOCK_AUTH = false;

const supabaseUrl = 'https://wdpprzemflzlardkmnfk.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndkcHByemVmbHpsYXJka21uY2ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDk4MjgsImV4cCI6MjA2NzY4NTgyOH0.yp_k9Sv6AMFZmUs_EWa_-rPZGyTxNNFTZOM4RaU668s'

const realSupabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// 根据开发模式选择使用真实 Supabase 还是模拟版本
export const supabase = USE_MOCK_AUTH ? mockSupabase : realSupabase;

// 导出开发模式标志
export { USE_MOCK_AUTH };

// 类型定义
export interface User {
  id: string
  email: string
  username?: string
  display_name?: string
  avatar_url?: string
  role?: 'user' | 'admin' | 'moderator'
  created_at: string
  updated_at?: string
  user_metadata?: {
    username?: string
    display_name?: string
  }
}

export interface AuthResponse {
  user: User | null
  session: any
  error: any
}

// 认证相关函数
export const authService = {
  // 用户注册
  async signUp(email: string, password: string, username: string, displayName?: string) {
    if (USE_MOCK_AUTH) {
      return await mockSupabase.auth.signUp(email, password, username, displayName);
    } else {
      const { data, error } = await realSupabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            display_name: displayName || username
          },
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });
      return { data, error };
    }
  },

  // 用户登录
  async signIn(email: string, password: string) {
    if (USE_MOCK_AUTH) {
      return await mockSupabase.auth.signInWithPassword(email, password);
    } else {
      const { data, error } = await realSupabase.auth.signInWithPassword({
        email,
        password
      });
      return { data, error };
    }
  },

  // 用户登出
  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // 获取当前用户
  async getCurrentUser() {
    if (USE_MOCK_AUTH) {
      const { data } = await (supabase.auth as any).getSession();
      return { user: data.session?.user || null, error: null };
    } else {
      const { data: { user }, error } = await realSupabase.auth.getUser();
      return { user, error };
    }
  },

  // 获取当前会话
  async getCurrentSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    return { session, error }
  },

  // 重置密码
  async resetPassword(email: string) {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    })
    
    return { data, error }
  },

  // 更新密码
  async updatePassword(password: string) {
    const { data, error } = await supabase.auth.updateUser({
      password
    })
    
    return { data, error }
  },

  // Google 社交登录
  async signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    
    return { data, error }
  },

  // Facebook 社交登录
  async signInWithFacebook() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'facebook',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    
    return { data, error }
  },

  // Twitter 社交登录
  async signInWithTwitter() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'twitter',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    
    return { data, error }
  },

  // Apple 社交登录
  async signInWithApple() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    })
    
    return { data, error }
  }
}

// 监听认证状态变化
export const onAuthStateChange = (callback: (event: string, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback)
}
