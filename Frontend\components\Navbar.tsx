'use client';

import { useAuth } from '@/contexts/AuthContext';
import { BellIcon, LogoutIcon } from './Icons';
import { useState, useEffect } from 'react';

interface NavbarProps {
  user: any;
}

export default function Navbar({ user }: NavbarProps) {
  const { logout } = useAuth();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogout = () => {
    logout();
  };

  return (
    <nav className="bg-white shadow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold">Dashboard</h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-gray-700">
              Welcome, {user?.name || user?.email}
            </span>
            
            {/* 通知按钮 - 只在客户端渲染 */}
            {mounted && (
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                aria-label="View notifications"
              >
                <BellIcon className="h-6 w-6" />
              </button>
            )}
            
            {/* 登出按钮 */}
            <button
              onClick={handleLogout}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              {mounted && <LogoutIcon className="h-4 w-4 mr-2" />}
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}