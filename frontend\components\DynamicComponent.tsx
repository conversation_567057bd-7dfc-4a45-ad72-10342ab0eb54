'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

// 动态导入组件，禁用 SSR
export function createDynamicComponent<T extends {}>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  fallback?: ComponentType<T>
) {
  return dynamic(importFunc, {
    ssr: false,
    loading: () => fallback ? <fallback {...({} as T)} /> : null,
  });
}

// 创建一个通用的客户端渲染包装器
export const ClientRenderWrapper = ({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode; 
  fallback?: React.ReactNode; 
}) => {
  return dynamic(
    () => Promise.resolve(() => <>{children}</>),
    {
      ssr: false,
      loading: () => <>{fallback}</>,
    }
  );
};