'use client';

// import Image from 'next/image'; // Temporarily disabled due to config issues
import { useState } from 'react';

interface AuthorCardProps {
  author: {
    name: string;
    avatar: string;
    username?: string;
  };
  publishedAt: string;
  readTime?: string;
  showFollowButton?: boolean;
  onFollow?: () => void;
  isFollowing?: boolean;
}

export default function AuthorCard({
  author,
  publishedAt,
  readTime,
  showFollowButton = false,
  onFollow,
  isFollowing = false
}: AuthorCardProps) {
  const [imageError, setImageError] = useState(false);
  
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return '1d ago';
    if (diffInDays < 7) return `${diffInDays}d ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)}w ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        {/* Author Avatar */}
        <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
          <img
            src={author.avatar}
            alt={author.name}
            className="object-cover w-full h-full"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(author.name)}&background=6366f1&color=fff&size=48`;
            }}
          />
        </div>

        {/* Author Info */}
        <div className="flex flex-col">
          <h3 className="font-semibold text-gray-900 text-base">
            {author.name}
          </h3>
          <div className="flex items-center space-x-2 text-sm text-blue-500">
            <span>Published {formatTimeAgo(publishedAt)}</span>
            {readTime && (
              <>
                <span>•</span>
                <span>{readTime} read</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Follow Button */}
      {showFollowButton && onFollow && (
        <button
          onClick={onFollow}
          className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 ${
            isFollowing
              ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg'
          }`}
        >
          {isFollowing ? 'Following' : 'Follow'}
        </button>
      )}
    </div>
  );
}
