'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabase, authService, User, onAuthStateChange } from '@/lib/supabase';
import { useRouter } from 'next/navigation';

interface AuthContextType {
  user: User | null;
  session: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, username: string, displayName?: string) => Promise<boolean>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (password: string) => Promise<boolean>;
  signInWithGoogle: () => Promise<void>;
  signInWithFacebook: () => Promise<void>;
  signInWithTwitter: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function SupabaseAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const router = useRouter();

  // 计算认证状态
  const isAuthenticated = !!(user && session);

  // 初始化认证状态
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        
        // 获取当前会话
        const { session: currentSession } = await authService.getCurrentSession();
        
        if (currentSession) {
          setSession(currentSession);
          setUser(currentSession.user as User);
          console.log('🔄 Supabase: 从会话恢复认证状态');
        }
      } catch (error) {
        console.error('❌ Supabase: 初始化认证状态失败:', error);
        setError('Failed to initialize authentication');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // 监听认证状态变化
  useEffect(() => {
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      console.log('🔄 Supabase: 认证状态变化:', event, session?.user?.email);
      
      if (session) {
        setSession(session);
        setUser(session.user);
        setError('');
      } else {
        setSession(null);
        setUser(null);
      }
      
      setIsLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  // 登录函数
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      
      const { data, error: authError } = await authService.signIn(email, password);
      
      if (authError) {
        setError(authError.message);
        return false;
      }
      
      if (data && data.user && data.session) {
        setUser(data.user as User);
        setSession(data.session);
        console.log('✅ Supabase: 用户登录成功');
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ Supabase: 登录失败:', error);
      setError(error.message || 'Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册函数
  const register = async (email: string, password: string, username: string, displayName?: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      
      const { data, error: authError } = await authService.signUp(email, password, username, displayName);
      
      if (authError) {
        setError(authError.message);
        return false;
      }
      
      if (data && data.user) {
        console.log('✅ Supabase: 用户注册成功，请检查邮箱验证');
        // 注册成功但需要邮箱验证
        return true;
      }
      
      return false;
    } catch (error: any) {
      console.error('❌ Supabase: 注册失败:', error);
      setError(error.message || 'Registration failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出函数
  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      const { error: authError } = await authService.signOut();
      
      if (authError) {
        console.error('❌ Supabase: 登出失败:', authError);
        setError(authError.message);
      } else {
        setUser(null);
        setSession(null);
        setError('');
        console.log('🚪 Supabase: 用户登出成功');
        router.push('/');
      }
    } catch (error: any) {
      console.error('❌ Supabase: 登出异常:', error);
      setError(error.message || 'Logout failed');
    } finally {
      setIsLoading(false);
    }
  };

  // 重置密码
  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      setError('');
      
      const { error: authError } = await authService.resetPassword(email);
      
      if (authError) {
        setError(authError.message);
        return false;
      }
      
      console.log('✅ Supabase: 密码重置邮件已发送');
      return true;
    } catch (error: any) {
      console.error('❌ Supabase: 密码重置失败:', error);
      setError(error.message || 'Password reset failed');
      return false;
    }
  };

  // 更新密码
  const updatePassword = async (password: string): Promise<boolean> => {
    try {
      setError('');
      
      const { error: authError } = await authService.updatePassword(password);
      
      if (authError) {
        setError(authError.message);
        return false;
      }
      
      console.log('✅ Supabase: 密码更新成功');
      return true;
    } catch (error: any) {
      console.error('❌ Supabase: 密码更新失败:', error);
      setError(error.message || 'Password update failed');
      return false;
    }
  };

  // 社交登录函数
  const signInWithGoogle = async (): Promise<void> => {
    try {
      setError('');
      const { error: authError } = await authService.signInWithGoogle();
      if (authError) {
        setError(authError.message);
      }
    } catch (error: any) {
      setError(error.message || 'Google sign in failed');
    }
  };

  const signInWithFacebook = async (): Promise<void> => {
    try {
      setError('');
      const { error: authError } = await authService.signInWithFacebook();
      if (authError) {
        setError(authError.message);
      }
    } catch (error: any) {
      setError(error.message || 'Facebook sign in failed');
    }
  };

  const signInWithTwitter = async (): Promise<void> => {
    try {
      setError('');
      const { error: authError } = await authService.signInWithTwitter();
      if (authError) {
        setError(authError.message);
      }
    } catch (error: any) {
      setError(error.message || 'Twitter sign in failed');
    }
  };

  const signInWithApple = async (): Promise<void> => {
    try {
      setError('');
      const { error: authError } = await authService.signInWithApple();
      if (authError) {
        setError(authError.message);
      }
    } catch (error: any) {
      setError(error.message || 'Apple sign in failed');
    }
  };

  // 清除错误
  const clearError = () => {
    setError('');
  };

  const value: AuthContextType = {
    user,
    session,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    resetPassword,
    updatePassword,
    signInWithGoogle,
    signInWithFacebook,
    signInWithTwitter,
    signInWithApple,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook for using auth context
export function useSupabaseAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
}
