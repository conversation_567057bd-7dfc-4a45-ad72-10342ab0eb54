// 开发环境完全禁用所有速率限制
const apiLimiter = (req, res, next) => next();
const authLimiter = (req, res, next) => next();
const passwordResetLimiter = (req, res, next) => next();
const emailVerificationLimiter = (req, res, next) => next();
const registrationLimiter = (req, res, next) => next();

module.exports = {
  apiLimiter,
  authLimiter,
  passwordResetLimiter,
  emailVerificationLimiter,
  registrationLimiter
};
