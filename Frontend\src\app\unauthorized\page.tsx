'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import AuthLayout from '@/components/AuthLayout';

export default function UnauthorizedPage() {
  const { user } = useAuth();
  const router = useRouter();

  return (
    <AuthLayout
      title="Access Denied"
      subtitle="You don't have permission to access this page"
      showBackButton
      backHref="/"
    >
      <div className="text-center space-y-4">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
          <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900">
            Insufficient Permissions
          </h3>
          <p className="text-sm text-gray-600">
            Sorry, you don&apos;t have permission to access this page. Please contact an administrator for the appropriate permissions.
          </p>
          {user && (
            <p className="text-sm text-gray-500">
              Current role: <span className="font-medium">{user.role === 'admin' ? 'Administrator' : user.role === 'moderator' ? 'Moderator' : 'User'}</span>
            </p>
          )}
        </div>

        <div className="space-y-3 pt-6">
          <button
            onClick={() => router.back()}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go Back
          </button>

          <Link
            href="/"
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
