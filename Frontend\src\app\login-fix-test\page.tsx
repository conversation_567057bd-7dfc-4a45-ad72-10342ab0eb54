'use client';

import React, { useState } from 'react';

export default function LoginFixTestPage() {
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setResult(prev => prev + `\n${new Date().toLocaleTimeString()}: ${message}`);
  };

  // 测试Gmail账户登录
  const testGmailLogin = async () => {
    setIsLoading(true);
    addResult('🔐 测试 Gmail 账户登录: <EMAIL>');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'TestPassword123!'
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addResult('✅ Gmail 登录成功！');
        addResult(`👤 用户信息: ${data.user.username} (${data.user.email})`);
        addResult(`🔑 Token: ${data.token.substring(0, 20)}...`);
      } else {
        addResult(`❌ Gmail 登录失败: ${data.message}`);
        addResult(`📊 响应状态: ${response.status}`);
      }
    } catch (error) {
      addResult(`💥 Gmail 登录异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试Hotmail账户登录
  const testHotmailLogin = async () => {
    setIsLoading(true);
    addResult('🔐 测试 Hotmail 账户登录: <EMAIL>');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'TestPassword123!'
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addResult('✅ Hotmail 登录成功！');
        addResult(`👤 用户信息: ${data.user.username} (${data.user.email})`);
        addResult(`🔑 Token: ${data.token.substring(0, 20)}...`);
      } else {
        addResult(`❌ Hotmail 登录失败: ${data.message}`);
        addResult(`📊 响应状态: ${response.status}`);
      }
    } catch (error) {
      addResult(`💥 Hotmail 登录异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试新用户注册
  const testNewRegistration = async () => {
    setIsLoading(true);
    const testEmail = `test${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    const testUsername = `user${Date.now()}`;
    
    addResult(`📝 测试新用户注册: ${testEmail}`);
    
    try {
      const response = await fetch('http://localhost:5000/api/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
          username: testUsername
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        addResult('✅ 注册成功！');
        addResult(`👤 新用户: ${testUsername} (${testEmail})`);
        
        // 立即测试登录
        addResult('🔐 立即测试新用户登录...');
        
        const loginResponse = await fetch('http://localhost:5000/api/users/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            identifier: testEmail,
            password: testPassword
          })
        });

        const loginData = await loginResponse.json();
        
        if (loginResponse.ok && loginData.success) {
          addResult('✅ 新用户登录成功！');
          addResult('🎉 注册-登录流程完全正常！');
        } else {
          addResult(`❌ 新用户登录失败: ${loginData.message}`);
        }
        
      } else {
        addResult(`❌ 注册失败: ${data.message}`);
        addResult(`📊 响应状态: ${response.status}`);
      }
    } catch (error) {
      addResult(`💥 注册异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除结果
  const clearResults = () => {
    setResult('');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-green-600">
            登录问题修复验证
          </h1>
          
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h2 className="text-lg font-semibold text-green-800 mb-2">修复说明</h2>
            <div className="text-green-700 space-y-1">
              <p><strong>问题</strong>: 注册成功但登录失败，显示"用户名或密码无效"</p>
              <p><strong>原因</strong>: 注册时使用了错误的字段名 `password` 而不是 `password_hash`</p>
              <p><strong>修复</strong>: 
                1. 修复了注册路由的字段名问题
                2. 为已注册的用户重新设置了正确的密码哈希
              </p>
              <p><strong>密码</strong>: TestPassword123!</p>
            </div>
          </div>

          {/* 测试按钮 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <button
              onClick={testGmailLogin}
              disabled={isLoading}
              className="bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
            >
              {isLoading ? '🔄' : '📧'} 测试 Gmail 登录
            </button>
            
            <button
              onClick={testHotmailLogin}
              disabled={isLoading}
              className="bg-orange-600 text-white py-3 px-4 rounded-md hover:bg-orange-700 disabled:opacity-50 flex items-center justify-center"
            >
              {isLoading ? '🔄' : '📧'} 测试 Hotmail 登录
            </button>
            
            <button
              onClick={testNewRegistration}
              disabled={isLoading}
              className="bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
            >
              {isLoading ? '🔄' : '📝'} 测试新用户注册+登录
            </button>
            
            <button
              onClick={clearResults}
              disabled={isLoading}
              className="bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 disabled:opacity-50 flex items-center justify-center"
            >
              🗑️ 清除结果
            </button>
          </div>

          {/* 一键测试所有功能 */}
          <div className="mb-6">
            <button
              onClick={async () => {
                setResult('🚀 开始完整测试流程...\n');
                await testGmailLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testHotmailLogin();
                await new Promise(resolve => setTimeout(resolve, 1000));
                await testNewRegistration();
                addResult('🎉 所有测试完成！');
              }}
              disabled={isLoading}
              className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50 text-lg font-semibold"
            >
              {isLoading ? '🔄 测试中...' : '🚀 一键测试所有功能'}
            </button>
          </div>

          {/* 结果显示 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm min-h-64">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            <pre className="whitespace-pre-wrap break-words">
              {result || '点击上方按钮开始测试...'}
            </pre>
          </div>

          {/* 账户信息 */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">测试账户信息</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>Gmail账户</strong>: <EMAIL> / TestPassword123!</p>
              <p><strong>Hotmail账户</strong>: <EMAIL> / TestPassword123!</p>
              <p><strong>注意</strong>: 这两个账户的密码已经修复，现在应该可以正常登录</p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a href="/final-auth-test" className="text-blue-600 hover:text-blue-800 underline">
              终极认证测试
            </a>
            <a href="/simple-auth-test" className="text-blue-600 hover:text-blue-800 underline">
              简单API测试
            </a>
            <a href="/" className="text-blue-600 hover:text-blue-800 underline">
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
