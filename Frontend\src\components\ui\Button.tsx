'use client';

import React, { forwardRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'gradient' | 'glass' | 'outline' | 'danger' | 'success' | 'warning';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  shape?: 'rounded' | 'pill' | 'square';
  effect?: 'none' | 'glow' | 'ripple' | 'magnetic' | 'bounce' | 'pulse';
  loading?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  animate?: boolean;
  gradient?: 'primary' | 'secondary' | 'accent' | 'rainbow';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    shape = 'rounded',
    effect = 'none',
    loading = false,
    icon,
    iconPosition = 'left',
    fullWidth = false,
    animate = true,
    gradient = 'primary',
    children,
    disabled,
    ...props
  }, ref) => {
    
    // 基础样式
    const baseStyles = [
      'inline-flex items-center justify-center gap-2',
      'font-medium transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'relative overflow-hidden',
      fullWidth && 'w-full'
    ];

    // 渐变配置
    const gradientConfigs = {
      primary: 'bg-gradient-primary',
      secondary: 'bg-gradient-secondary',
      accent: 'bg-gradient-accent',
      rainbow: 'bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500'
    };

    // 变体样式
    const variantStyles = {
      primary: [
        gradientConfigs[gradient],
        'text-white',
        'hover:shadow-lg hover:shadow-primary/25',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-primary-500',
        'before:absolute before:inset-0',
        'before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent',
        'before:translate-x-[-100%] before:transition-transform before:duration-500',
        'hover:before:translate-x-[100%]'
      ].filter(Boolean),
      secondary: [
        'bg-surface-2 text-text-primary',
        'hover:bg-surface hover:shadow-lg',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-primary-500',
        'border border-border'
      ].filter(Boolean),
      gradient: [
        gradientConfigs[gradient],
        'text-white',
        'hover:shadow-lg hover:shadow-accent/25',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-accent-500'
      ].filter(Boolean),
      glass: [
        'bg-white/10 backdrop-blur-md border border-white/20',
        'text-text-primary hover:bg-white/20',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-primary-500'
      ].filter(Boolean),
      ghost: [
        'bg-transparent text-text-primary',
        'hover:bg-surface-2',
        'focus:ring-primary-500'
      ],
      outline: [
        'border-2 border-primary bg-transparent text-primary',
        'hover:bg-primary hover:text-white',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-primary-500'
      ].filter(Boolean),
      danger: [
        'bg-gradient-to-r from-red-500 to-red-600 text-white',
        'hover:from-red-600 hover:to-red-700',
        'hover:shadow-lg hover:shadow-red-500/25',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-red-500'
      ].filter(Boolean),
      success: [
        'bg-gradient-to-r from-green-500 to-green-600 text-white',
        'hover:from-green-600 hover:to-green-700',
        'hover:shadow-lg hover:shadow-green-500/25',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-green-500'
      ].filter(Boolean),
      warning: [
        'bg-gradient-to-r from-yellow-500 to-orange-500 text-white',
        'hover:from-yellow-600 hover:to-orange-600',
        'hover:shadow-lg hover:shadow-yellow-500/25',
        animate && 'hover:-translate-y-0.5',
        'focus:ring-yellow-500'
      ].filter(Boolean)
    };

    // 尺寸样式
    const sizeStyles = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl'
    };

    // 形状样式
    const shapeStyles = {
      rounded: 'rounded-lg',
      pill: 'rounded-full',
      square: 'rounded-none'
    };

    // 效果样式
    const effectStyles = {
      none: '',
      glow: [
        'hover:shadow-2xl hover:shadow-primary/30',
        'transition-shadow duration-300'
      ],
      ripple: [
        'active:scale-95',
        'transition-transform duration-150'
      ],
      magnetic: [
        'hover:scale-105',
        'transition-transform duration-300'
      ],
      bounce: [
        'hover:animate-bounce',
        'active:scale-95'
      ],
      pulse: [
        'hover:animate-pulse',
        'transition-all duration-300'
      ]
    };

    const buttonClasses = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      shapeStyles[shape],
      effectStyles[effect],
      className
    );

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
        )}
        
        {icon && iconPosition === 'left' && !loading && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        
        {children && (
          <span className={loading ? 'opacity-0' : ''}>{children}</span>
        )}
        
        {icon && iconPosition === 'right' && !loading && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button };
