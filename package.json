{"name": "newzora", "version": "1.0.0", "description": "Modern News Content Platform - Full Stack Application", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd Backend && npm run dev", "dev:frontend": "cd Frontend && npm run dev", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd Backend && npm start", "start:frontend": "cd Frontend && npm start", "build": "npm run build:frontend", "build:frontend": "cd Frontend && npm run build", "build:production": "npm run build:frontend && npm run build:backend", "build:backend": "cd Backend && npm run build", "install:all": "npm install && cd Backend && npm install && cd ../Frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd Backend && rm -rf node_modules package-lock.json", "clean:frontend": "cd Frontend && rm -rf node_modules package-lock.json .next", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd Backend && npm test", "test:frontend": "cd Frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd Backend && npm run lint", "lint:frontend": "cd Frontend && npm run lint", "deploy": "npm run build:production && npm run start:production", "start:production": "NODE_ENV=production concurrently \"npm run start:backend\" \"npm run start:frontend\"", "docker:build": "docker-compose -f deployment/docker/docker-compose.yml build", "docker:up": "docker-compose -f deployment/docker/docker-compose.yml up -d", "docker:down": "docker-compose -f deployment/docker/docker-compose.yml down", "docker:logs": "docker-compose -f deployment/docker/docker-compose.yml logs -f", "health:check": "curl -f http://localhost:5000/api/health && curl -f http://localhost:3000"}, "keywords": ["news", "content", "platform", "nextjs", "express", "postgresql", "typescript", "tailwindcss"], "author": "Newzora Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/Jacken22/Newzora.git"}, "dependencies": {"node-fetch": "^3.3.2"}}