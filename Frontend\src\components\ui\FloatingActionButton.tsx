'use client';

import React, { forwardRef, ReactNode, useState } from 'react';
import { cn } from '@/lib/utils';

export interface FloatingActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  icon: ReactNode;
  label?: string;
  extended?: boolean;
  tooltip?: string;
}

const FloatingActionButton = forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    position = 'bottom-right',
    icon,
    label,
    extended = false,
    tooltip,
    disabled,
    ...props
  }, ref) => {
    const [isHovered, setIsHovered] = useState(false);
    
    // 基础样式
    const baseStyles = [
      'fixed z-50',
      'inline-flex items-center justify-center gap-2',
      'font-medium transition-all duration-300',
      'focus:outline-none focus:ring-4 focus:ring-offset-2',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'shadow-2xl hover:shadow-3xl',
      'rounded-full',
      'group'
    ];

    // 位置样式
    const positionStyles = {
      'bottom-right': 'bottom-6 right-6',
      'bottom-left': 'bottom-6 left-6',
      'top-right': 'top-6 right-6',
      'top-left': 'top-6 left-6'
    };

    // 变体样式
    const variantStyles = {
      primary: [
        'bg-gradient-primary text-white',
        'hover:shadow-primary/30',
        'focus:ring-primary-500/50',
        'hover:scale-110'
      ],
      secondary: [
        'bg-gradient-secondary text-white',
        'hover:shadow-secondary/30',
        'focus:ring-secondary-500/50',
        'hover:scale-110'
      ],
      danger: [
        'bg-gradient-to-r from-red-500 to-red-600 text-white',
        'hover:shadow-red-500/30',
        'focus:ring-red-500/50',
        'hover:scale-110'
      ]
    };

    // 尺寸样式
    const sizeStyles = {
      sm: extended ? 'h-12 px-4' : 'w-12 h-12',
      md: extended ? 'h-14 px-6' : 'w-14 h-14',
      lg: extended ? 'h-16 px-8' : 'w-16 h-16'
    };

    const iconSizes = {
      sm: 'w-5 h-5',
      md: 'w-6 h-6',
      lg: 'w-7 h-7'
    };

    const buttonClasses = cn(
      baseStyles,
      positionStyles[position],
      variantStyles[variant],
      sizeStyles[size],
      className
    );

    return (
      <>
        <button
          ref={ref}
          className={buttonClasses}
          disabled={disabled}
          title={tooltip}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          {...props}
        >
          <span className={cn('transition-transform duration-300', iconSizes[size])}>
            {icon}
          </span>
          
          {(extended || (label && isHovered)) && (
            <span className={cn(
              'font-semibold transition-all duration-300',
              extended ? 'opacity-100 max-w-none' : 'opacity-0 max-w-0 overflow-hidden',
              isHovered && !extended && 'opacity-100 max-w-xs ml-2'
            )}>
              {label}
            </span>
          )}
        </button>

        {/* Ripple effect */}
        <style jsx>{`
          @keyframes ripple {
            0% {
              transform: scale(0);
              opacity: 1;
            }
            100% {
              transform: scale(4);
              opacity: 0;
            }
          }
          
          .fab-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
          }
        `}</style>
      </>
    );
  }
);

FloatingActionButton.displayName = 'FloatingActionButton';

export { FloatingActionButton };
