'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import UserProfileModal from '@/components/UserProfileModal';

interface Comment {
  id: number;
  author: {
    id?: number;
    name: string;
    avatar: string;
    username?: string;
  };
  content: string;
  createdAt: string;
  likes: number;
  isLiked?: boolean;
}

interface CommentSectionProps {
  articleId: number;
  initialComments?: Comment[];
  currentUser?: {
    name: string;
    avatar: string;
  };
}

export default function CommentSection({
  articleId,
  initialComments = [],
  currentUser = {
    name: "You",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face"
  }
}: CommentSectionProps) {
  const { isAuthenticated, user, token } = useAuth();
  const toast = useToast();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUsername, setSelectedUsername] = useState<string | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);

  useEffect(() => {
    fetchComments();
  }, [articleId]);

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await fetch(`http://localhost:5000/api/comments/article/${articleId}`);
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      setComments([]);
    } finally {
      setLoading(false);
    }
  };



  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    // Check if user is authenticated
    if (!isAuthenticated || !token) {
      toast.warning('Please log in first', 'You need to log in to post comments');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('http://localhost:5000/api/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          articleId: articleId,
          content: newComment.trim(),
        }),
      });

      if (response.ok) {
        const newCommentData = await response.json();
        setComments(prev => [newCommentData, ...prev]);
        setNewComment('');
        toast.success('Comment posted successfully!');
      } else {
        throw new Error('Failed to post comment');
      }
    } catch (error) {
      console.error('Error posting comment:', error);
      toast.error('Failed to post comment', 'Please try again later');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: number) => {
    // Check if user is authenticated
    if (!isAuthenticated || !token) {
      toast.warning('Please log in first', 'You need to log in to like comments');
      return;
    }

    try {
      const response = await fetch(`http://localhost:5000/api/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setComments(prev => prev.map(comment =>
          comment.id === commentId
            ? { ...comment, isLiked: !comment.isLiked, likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1 }
            : comment
        ));
      } else {
        throw new Error('Failed to like comment');
      }
    } catch (error) {
      console.error('Error liking comment:', error);
      toast.error('Failed to like comment', 'Please try again later');
    }
  };

  const handleUserClick = (author: Comment['author']) => {
    if (author.id && author.username) {
      setSelectedUserId(author.id);
      setSelectedUsername(author.username);
      setShowUserModal(true);
    } else {
      toast.info('User profile not available');
    }
  };

  return (
    <section className="mt-12">
      <h2 className="text-2xl font-bold text-gray-900 mb-8">Comments</h2>

      {/* Comment Input */}
      <div className="mb-8">
        {isAuthenticated && user ? (
          <form onSubmit={handleSubmitComment} className="flex space-x-4">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.username}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.username)}&background=6366f1&color=fff&size=48`;
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <span className="text-white font-medium text-lg">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 focus:bg-white transition-all duration-200 hover:bg-gray-100/50 resize-none"
                rows={3}
              />
              <div className="flex justify-end mt-3">
                <button
                  type="submit"
                  disabled={!newComment.trim() || isSubmitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {isSubmitting ? 'Posting...' : 'Post Comment'}
                </button>
              </div>
            </div>
          </form>
        ) : (
          <div className="flex space-x-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
            <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
              <span className="text-gray-500 text-lg">👤</span>
            </div>
            <div className="flex-1">
              <p className="text-gray-600 mb-3">Please log in to post comments</p>
              <button
                onClick={() => toast.info('Please log in to comment')}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                Log In to Comment
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Comments List */}
      <div className="space-y-8">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading comments...</p>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <p>No comments yet. Be the first to share your thoughts!</p>
          </div>
        ) : (
          comments.map((comment) => (
          <div key={comment.id} className="flex space-x-4">
            <div
              className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-500/30 transition-all duration-200"
              onClick={() => handleUserClick(comment.author)}
              title="Click to view profile"
            >
              <img
                src={comment.author.avatar}
                alt={comment.author.name}
                className="object-cover w-full h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.name)}&background=6366f1&color=fff&size=48`;
                }}
              />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h4
                  className="font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                  onClick={() => handleUserClick(comment.author)}
                  title="Click to view profile"
                >
                  {comment.author.name}
                </h4>
                {comment.author.username && (
                  <span className="text-gray-500 text-sm">@{comment.author.username}</span>
                )}
                <span className="text-gray-500 text-sm">{comment.createdAt}</span>
              </div>
              <p className="text-gray-700 leading-relaxed mb-3">
                {comment.content}
              </p>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleLikeComment(comment.id)}
                  className={`flex items-center space-x-1 text-sm transition-colors duration-200 ${
                    comment.isLiked
                      ? 'text-red-500'
                      : 'text-gray-500 hover:text-red-500'
                  }`}
                  title={isAuthenticated ? 'Like this comment' : 'Please log in to like'}
                >
                  <svg
                    className="w-4 h-4"
                    fill={comment.isLiked ? 'currentColor' : 'none'}
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                  <span>{comment.likes}</span>
                </button>
                <button
                  className="text-sm text-gray-500 hover:text-blue-500 transition-colors duration-200"
                  onClick={() => {
                    if (!isAuthenticated) {
                      toast.warning('Please log in first', 'You need to log in to reply to comments');
                    }
                  }}
                  title={isAuthenticated ? 'Reply to this comment' : 'Please log in to reply'}
                >
                  Reply
                </button>
              </div>
            </div>
          </div>
          ))
        )}
      </div>

      {/* User Profile Modal */}
      <UserProfileModal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        userId={selectedUserId || 0}
        username={selectedUsername || undefined}
      />
    </section>
  );
}
