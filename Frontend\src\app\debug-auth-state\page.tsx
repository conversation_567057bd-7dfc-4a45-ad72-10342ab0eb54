'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function DebugAuthStatePage() {
  const { user, token, isAuthenticated, isLoading, error, isHydrated } = useAuth();
  const [localStorageData, setLocalStorageData] = useState<any>({});
  const [sessionStorageData, setSessionStorageData] = useState<any>({});

  useEffect(() => {
    // 读取 localStorage 数据
    const authToken = localStorage.getItem('auth_token');
    const authUser = localStorage.getItem('auth_user');
    const authenticated = localStorage.getItem('authenticated');
    const loginTimestamp = localStorage.getItem('login_timestamp');

    setLocalStorageData({
      auth_token: authToken,
      auth_user: authUser ? JSON.parse(authUser) : null,
      authenticated,
      login_timestamp: loginTimestamp,
    });

    // 读取 sessionStorage 数据
    const authVerified = sessionStorage.getItem('auth_verified');
    const authenticatedSession = sessionStorage.getItem('authenticated_session');
    const loginSuccess = sessionStorage.getItem('login_success');

    setSessionStorageData({
      auth_verified: authVerified,
      authenticated_session: authenticatedSession,
      login_success: loginSuccess,
    });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">认证状态调试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* AuthContext 状态 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">AuthContext 状态</h2>
            <div className="space-y-2">
              <p><strong>用户:</strong> {user ? JSON.stringify(user, null, 2) : 'null'}</p>
              <p><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'null'}</p>
              <p><strong>已认证:</strong> {isAuthenticated ? '是' : '否'}</p>
              <p><strong>加载中:</strong> {isLoading ? '是' : '否'}</p>
              <p><strong>已水合:</strong> {isHydrated ? '是' : '否'}</p>
              <p><strong>错误:</strong> {error || '无'}</p>
            </div>
          </div>

          {/* localStorage 数据 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">localStorage 数据</h2>
            <div className="space-y-2">
              <p><strong>auth_token:</strong> {localStorageData.auth_token ? `${localStorageData.auth_token.substring(0, 20)}...` : 'null'}</p>
              <p><strong>auth_user:</strong> {localStorageData.auth_user ? JSON.stringify(localStorageData.auth_user, null, 2) : 'null'}</p>
              <p><strong>authenticated:</strong> {localStorageData.authenticated || 'null'}</p>
              <p><strong>login_timestamp:</strong> {localStorageData.login_timestamp || 'null'}</p>
            </div>
          </div>

          {/* sessionStorage 数据 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">sessionStorage 数据</h2>
            <div className="space-y-2">
              <p><strong>auth_verified:</strong> {sessionStorageData.auth_verified || 'null'}</p>
              <p><strong>authenticated_session:</strong> {sessionStorageData.authenticated_session || 'null'}</p>
              <p><strong>login_success:</strong> {sessionStorageData.login_success || 'null'}</p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">调试操作</h2>
            <div className="space-y-2">
              <button
                onClick={() => {
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
                className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                清除所有存储并刷新
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                刷新页面
              </button>
              <button
                onClick={() => window.location.href = '/login'}
                className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                跳转到登录页
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
