'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';

export default function DebugAuthStatePage() {
  const { user, token, isAuthenticated, isLoading } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);

  // 监控认证状态变化
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] isLoading: ${isLoading}, isAuthenticated: ${isAuthenticated}, hasUser: ${!!user}, hasToken: ${!!token}`;
    
    console.log('🔍 Auth State Change:', logEntry);
    setLogs(prev => [logEntry, ...prev.slice(0, 19)]); // 保留最新20条记录
  }, [isLoading, isAuthenticated, user, token]);

  const clearLogs = () => {
    setLogs([]);
  };

  const checkLocalStorage = () => {
    const savedToken = localStorage.getItem('auth_token');
    const savedUser = localStorage.getItem('auth_user');
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] localStorage - token: ${!!savedToken}, user: ${!!savedUser}`;
    
    console.log('💾 LocalStorage Check:', logEntry);
    setLogs(prev => [logEntry, ...prev.slice(0, 19)]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">认证状态实时监控</h1>
        
        {/* 当前状态 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">当前状态</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className={`p-3 rounded-lg ${isLoading ? 'bg-yellow-100' : 'bg-green-100'}`}>
              <div className="text-sm font-medium">Loading</div>
              <div className="text-lg">{isLoading ? '是' : '否'}</div>
            </div>
            <div className={`p-3 rounded-lg ${isAuthenticated ? 'bg-green-100' : 'bg-red-100'}`}>
              <div className="text-sm font-medium">Authenticated</div>
              <div className="text-lg">{isAuthenticated ? '是' : '否'}</div>
            </div>
            <div className={`p-3 rounded-lg ${user ? 'bg-green-100' : 'bg-red-100'}`}>
              <div className="text-sm font-medium">User</div>
              <div className="text-lg">{user ? user.username : '无'}</div>
            </div>
            <div className={`p-3 rounded-lg ${token ? 'bg-green-100' : 'bg-red-100'}`}>
              <div className="text-sm font-medium">Token</div>
              <div className="text-lg">{token ? '有' : '无'}</div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">操作</h2>
          <div className="flex gap-4">
            <button
              onClick={checkLocalStorage}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              检查 LocalStorage
            </button>
            <button
              onClick={clearLogs}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              清除日志
            </button>
            <a
              href="/login"
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 text-center"
            >
              去登录页面
            </a>
            <a
              href="/"
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-center"
            >
              去主页
            </a>
          </div>
        </div>

        {/* 状态变化日志 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">状态变化日志</h2>
          <div className="bg-gray-100 rounded-lg p-4 max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500">暂无日志记录</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
