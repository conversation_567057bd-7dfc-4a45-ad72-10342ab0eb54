'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

type Theme = 'light' | 'dark' | 'cyberpunk' | 'nature' | 'ocean' | 'sunset' | 'auto';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark' | 'cyberpunk' | 'nature' | 'ocean' | 'sunset';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
}

export function ThemeProvider({ children, defaultTheme = 'auto' }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme);
  const [actualTheme, setActualTheme] = useState<'light' | 'dark' | 'cyberpunk' | 'nature' | 'ocean' | 'sunset'>('light');

  // 从localStorage读取主题设置
  useEffect(() => {
    const savedTheme = localStorage.getItem('newzora-theme') as Theme;
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = () => {
      if (theme === 'auto') {
        setActualTheme(mediaQuery.matches ? 'dark' : 'light');
      }
    };

    handleChange(); // 初始检查
    mediaQuery.addEventListener('change', handleChange);
    
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // 更新实际主题
  useEffect(() => {
    if (theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      setActualTheme(mediaQuery.matches ? 'dark' : 'light');
    } else {
      setActualTheme(theme);
    }
  }, [theme]);

  // 应用主题到DOM
  useEffect(() => {
    const root = document.documentElement;
    
    // 移除所有主题类
    root.removeAttribute('data-theme');
    root.classList.remove('light', 'dark', 'cyberpunk', 'nature', 'ocean', 'sunset');
    
    // 应用新主题
    if (actualTheme !== 'light') {
      root.setAttribute('data-theme', actualTheme);
    }
    root.classList.add(actualTheme);
    
    // 保存到localStorage
    localStorage.setItem('newzora-theme', theme);
  }, [theme, actualTheme]);

  const handleSetTheme = (newTheme: Theme) => {
    setTheme(newTheme);
  };

  const value = {
    theme,
    setTheme: handleSetTheme,
    actualTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// 主题配置
export const themes = {
  light: {
    name: 'Light Mode',
    description: '明亮清新的白色主题',
    icon: '☀️'
  },
  dark: {
    name: 'Dark Mode', 
    description: '护眼的深色主题',
    icon: '🌙'
  },
  cyberpunk: {
    name: 'Cyberpunk',
    description: '未来科技感主题',
    icon: '🔮'
  },
  nature: {
    name: 'Nature',
    description: '清新自然的绿色主题',
    icon: '🌿'
  },
  ocean: {
    name: 'Ocean',
    description: '深邃海洋的蓝色主题',
    icon: '🌊'
  },
  sunset: {
    name: 'Sunset',
    description: '温暖日落的橙色主题',
    icon: '🌅'
  },
  auto: {
    name: 'Auto',
    description: '跟随系统设置',
    icon: '🔄'
  }
} as const;

// 主题切换组件
export function ThemeToggle() {
  const { theme, setTheme, actualTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const currentTheme = themes[theme];

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 rounded-lg bg-surface hover:bg-surface-2 transition-colors"
        aria-label="切换主题"
      >
        <span className="text-lg">{currentTheme.icon}</span>
        <span className="text-sm font-medium text-text-primary">
          {currentTheme.name}
        </span>
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-48 bg-surface border border-border rounded-xl shadow-lg z-50 animate-fade-in">
          <div className="p-2">
            {Object.entries(themes).map(([key, themeConfig]) => (
              <button
                key={key}
                onClick={() => {
                  setTheme(key as Theme);
                  setIsOpen(false);
                }}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  theme === key
                    ? 'bg-primary text-white'
                    : 'hover:bg-surface-2 text-text-primary'
                }`}
              >
                <span className="text-lg">{themeConfig.icon}</span>
                <div>
                  <div className="font-medium">{themeConfig.name}</div>
                  <div className="text-xs opacity-70">{themeConfig.description}</div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 点击外部关闭 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
