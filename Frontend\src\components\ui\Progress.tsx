'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

export interface ProgressProps {
  value: number;
  max?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'gradient' | 'striped' | 'animated';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  showLabel?: boolean;
  label?: string;
  className?: string;
  animated?: boolean;
}

export function Progress({
  value,
  max = 100,
  size = 'md',
  variant = 'default',
  color = 'primary',
  showLabel = false,
  label,
  className,
  animated = false
}: ProgressProps) {
  const [displayValue, setDisplayValue] = useState(0);
  
  // 动画效果
  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setDisplayValue(value);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setDisplayValue(value);
    }
  }, [value, animated]);

  const percentage = Math.min(Math.max((displayValue / max) * 100, 0), 100);

  // 尺寸样式
  const sizeStyles = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  // 颜色样式
  const colorStyles = {
    primary: 'bg-primary',
    secondary: 'bg-secondary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    danger: 'bg-red-500'
  };

  // 变体样式
  const getVariantStyles = () => {
    switch (variant) {
      case 'gradient':
        return 'bg-gradient-to-r from-primary to-secondary';
      case 'striped':
        return cn(
          colorStyles[color],
          'bg-stripes bg-stripes-white/20'
        );
      case 'animated':
        return cn(
          colorStyles[color],
          'bg-stripes bg-stripes-white/20 animate-stripes'
        );
      default:
        return colorStyles[color];
    }
  };

  return (
    <div className={cn('w-full', className)}>
      {/* 标签 */}
      {(showLabel || label) && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-text-primary">
            {label || 'Progress'}
          </span>
          <span className="text-sm text-text-muted">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
      
      {/* 进度条容器 */}
      <div className={cn(
        'w-full bg-surface-2 rounded-full overflow-hidden',
        sizeStyles[size]
      )}>
        {/* 进度条填充 */}
        <div
          className={cn(
            'h-full rounded-full transition-all duration-500 ease-out',
            getVariantStyles()
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}

// 圆形进度条
export function CircularProgress({
  value,
  max = 100,
  size = 80,
  strokeWidth = 8,
  color = 'primary',
  showLabel = true,
  label,
  className
}: {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  showLabel?: boolean;
  label?: string;
  className?: string;
}) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const colorStyles = {
    primary: 'stroke-primary',
    secondary: 'stroke-secondary',
    success: 'stroke-green-500',
    warning: 'stroke-yellow-500',
    danger: 'stroke-red-500'
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* 背景圆 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-surface-2"
        />
        
        {/* 进度圆 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            'transition-all duration-500 ease-out',
            colorStyles[color]
          )}
        />
      </svg>
      
      {/* 中心标签 */}
      {showLabel && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg font-bold text-text-primary">
              {Math.round(percentage)}%
            </div>
            {label && (
              <div className="text-xs text-text-muted">
                {label}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// 步骤进度条
export function StepProgress({
  steps,
  currentStep,
  className
}: {
  steps: Array<{
    title: string;
    description?: string;
  }>;
  currentStep: number;
  className?: string;
}) {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isUpcoming = index > currentStep;

          return (
            <div key={index} className="flex items-center">
              {/* 步骤圆圈 */}
              <div className={cn(
                'flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300',
                isCompleted && 'bg-primary border-primary text-white',
                isCurrent && 'border-primary text-primary bg-primary/10',
                isUpcoming && 'border-border text-text-muted'
              )}>
                {isCompleted ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>

              {/* 步骤信息 */}
              <div className="ml-3 flex-1">
                <div className={cn(
                  'text-sm font-medium',
                  isCompleted && 'text-primary',
                  isCurrent && 'text-text-primary',
                  isUpcoming && 'text-text-muted'
                )}>
                  {step.title}
                </div>
                {step.description && (
                  <div className="text-xs text-text-muted mt-1">
                    {step.description}
                  </div>
                )}
              </div>

              {/* 连接线 */}
              {index < steps.length - 1 && (
                <div className={cn(
                  'flex-1 h-0.5 mx-4 transition-colors duration-300',
                  index < currentStep ? 'bg-primary' : 'bg-border'
                )} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
