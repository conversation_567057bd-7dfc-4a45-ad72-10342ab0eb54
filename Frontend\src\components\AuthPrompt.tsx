'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface AuthPromptProps {
  isOpen: boolean;
  onClose: () => void;
  action: string; // 'like', 'comment', 'bookmark', 'follow', etc.
  title?: string;
}

export default function AuthPrompt({ 
  isOpen, 
  onClose, 
  action,
  title = "Sign in required"
}: AuthPromptProps) {
  const router = useRouter();

  if (!isOpen) return null;

  const getActionText = () => {
    switch (action) {
      case 'like':
        return 'like this content';
      case 'comment':
        return 'leave a comment';
      case 'bookmark':
        return 'bookmark this content';
      case 'follow':
        return 'follow this creator';
      case 'share':
        return 'share this content';
      case 'tip':
        return 'tip this creator';
      default:
        return 'perform this action';
    }
  };

  const getActionIcon = () => {
    switch (action) {
      case 'like':
        return '❤️';
      case 'comment':
        return '💬';
      case 'bookmark':
        return '🔖';
      case 'follow':
        return '👥';
      case 'share':
        return '📤';
      case 'tip':
        return '💰';
      default:
        return '🔐';
    }
  };

  const handleSignIn = () => {
    onClose();
    router.push('/login');
  };

  const handleSignUp = () => {
    onClose();
    router.push('/register');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6 relative animate-fade-in">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <XMarkIcon className="w-6 h-6" />
        </button>

        {/* Content */}
        <div className="text-center">
          {/* Icon */}
          <div className="text-6xl mb-4">
            {getActionIcon()}
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {title}
          </h2>

          {/* Description */}
          <p className="text-gray-600 mb-6">
            You need to sign in to {getActionText()}. Join our community to interact with creators and discover amazing content.
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleSignIn}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Sign In
            </button>
            
            <button
              onClick={handleSignUp}
              className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Create Account
            </button>
          </div>

          {/* Additional Info */}
          <div className="mt-6 pt-4 border-t border-gray-100">
            <p className="text-sm text-gray-500">
              Already have an account? 
              <button
                onClick={handleSignIn}
                className="text-blue-600 hover:text-blue-700 ml-1 font-medium"
              >
                Sign in here
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for using auth prompt
export function useAuthPrompt() {
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState('');

  const showPrompt = (actionType: string) => {
    setAction(actionType);
    setIsOpen(true);
  };

  const hidePrompt = () => {
    setIsOpen(false);
    setAction('');
  };

  return {
    isOpen,
    action,
    showPrompt,
    hidePrompt
  };
}
