'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';

interface PageNotification {
  id: string;
  type: 'comment' | 'like' | 'follow' | 'system' | 'collection';
  user?: {
    name: string;
    avatar: string;
  };
  message: string;
  time: string;
  isRead: boolean;
  actionUrl?: string;
  title?: string;
  content?: string;
  createdAt?: string;
  priority?: 'normal' | 'urgent';
  data?: {
    isRead: boolean;
  };
}

const mockNotifications: PageNotification[] = [
  {
    id: '1',
    type: 'comment',
    user: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face'
    },
    message: '<PERSON> replied to your comment: "Great insights!"',
    time: '2h',
    isRead: false
  },
  {
    id: '2',
    type: 'like',
    user: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face'
    },
    message: '<PERSON> liked your post about travel tips.',
    time: '4h',
    isRead: false
  },
  {
    id: '3',
    type: 'follow',
    user: {
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face'
    },
    message: 'You have a new follower, Olivia!',
    time: '6h',
    isRead: false
  },
  {
    id: '4',
    type: 'system',
    message: 'System Announcement: New feature update!',
    time: '1d',
    isRead: false
  },
  {
    id: '5',
    type: 'comment',
    user: {
      name: 'Ethan',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face'
    },
    message: 'Ethan mentioned you in a comment.',
    time: '2d',
    isRead: false
  },
  {
    id: '6',
    type: 'collection',
    message: 'Your post about photography was added to a collection.',
    time: '3d',
    isRead: false
  }
];

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState<'all' | 'mentions'>('all');
  const [notifications, setNotifications] = useState<PageNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications from API
  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to fetch from API first
      const response = await fetch('http://localhost:5000/api/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        // Transform API data to match our interface
        const transformedNotifications = data.notifications?.map((notif: any) => ({
          id: notif.id.toString(),
          type: notif.type,
          user: notif.user ? {
            name: notif.user.name || notif.user.username,
            avatar: notif.user.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face'
          } : undefined,
          message: notif.content || notif.message,
          time: formatTimeAgo(notif.createdAt),
          isRead: notif.isRead || false,
          actionUrl: notif.actionUrl
        })) || [];

        setNotifications(transformedNotifications);
      } else {
        // Fallback to mock data if API fails
        console.log('API failed, using mock data');
        setNotifications(mockNotifications);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      // Fallback to mock data
      setNotifications(mockNotifications);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d`;
    return date.toLocaleDateString();
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'mentions' && notification.type !== 'comment') return false;
    return true;
  });

  const handleNotificationClick = async (notificationId: string, actionUrl?: string) => {
    try {
      // Mark as read in backend
      await fetch(`http://localhost:5000/api/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }

    // Update local state
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      )
    );

    // Navigate to action URL if provided
    if (actionUrl) {
      window.open(actionUrl, '_blank');
    }
  };

  const markAllAsRead = async () => {
    try {
      // Mark all as read in backend
      await fetch('http://localhost:5000/api/notifications/mark-all-read', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }

    // Update local state
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const deleteNotification = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the click handler

    try {
      await fetch(`http://localhost:5000/api/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });

      // Remove from local state
      setNotifications(prev =>
        prev.filter(notification => notification.id !== notificationId)
      );
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'comment':
        return (
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        );
      case 'like':
        return (
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
          </div>
        );
      case 'follow':
        return (
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        );
      case 'system':
        return (
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
          </div>
        );
      case 'collection':
        return (
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h10v-1a3 3 0 00-3-3H7a3 3 0 00-3 3v1zM6 10a3 3 0 116 0 3 3 0 01-6 0z" />
            </svg>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={fetchNotifications}
              disabled={loading}
              className="text-sm text-gray-600 hover:text-gray-800 font-medium flex items-center space-x-1"
            >
              <svg className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Refresh</span>
            </button>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Mark all as read
              </button>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-0 border-b border-gray-200 mb-6">
          <button
            onClick={() => setActiveTab('all')}
            className={`pb-3 px-4 text-base font-medium transition-colors duration-200 border-b-2 flex items-center space-x-2 ${
              activeTab === 'all'
                ? 'text-blue-600 border-blue-600'
                : 'text-gray-500 hover:text-gray-700 border-transparent'
            }`}
          >
            <span>All</span>
            {notifications.length > 0 && (
              <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                {notifications.length}
              </span>
            )}
          </button>
          <button
            onClick={() => setActiveTab('mentions')}
            className={`pb-3 px-4 text-base font-medium transition-colors duration-200 border-b-2 flex items-center space-x-2 ${
              activeTab === 'mentions'
                ? 'text-blue-600 border-blue-600'
                : 'text-gray-500 hover:text-gray-700 border-transparent'
            }`}
          >
            <span>Mentions</span>
            {notifications.filter(n => n.type === 'comment').length > 0 && (
              <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                {notifications.filter(n => n.type === 'comment').length}
              </span>
            )}
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading notifications...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
                <button
                  onClick={fetchNotifications}
                  className="mt-2 text-sm text-red-600 hover:text-red-800 font-medium"
                >
                  Try again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notifications List */}
        {!loading && !error && (
          <div className="space-y-0">
            {filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleNotificationClick(notification.id, notification.actionUrl);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`Notification: ${notification.message}`}
              className={`group flex items-start space-x-4 p-4 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset transition-all duration-200 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                !notification.isRead ? 'bg-blue-50/30' : 'bg-white'
              }`}
            >
              {/* Avatar or Icon */}
              <div className="flex-shrink-0">
                {notification.user ? (
                  <img
                    src={notification.user.avatar}
                    alt={notification.user.name}
                    className="w-12 h-12 rounded-full object-cover"
                    onError={(e) => {
                      // 头像加载失败时显示默认头像
                      const target = e.target as HTMLImageElement;
                      target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(notification.user?.name || 'User')}&background=6366f1&color=fff&size=48`;
                    }}
                  />
                ) : (
                  getNotificationIcon(notification.type)
                )}
              </div>

              {/* Notification Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-baseline space-x-1">
                      <span className="font-semibold text-gray-900 text-sm">
                        {notification.user?.name ||
                         (notification.type === 'system' ? 'Newzora' :
                          notification.type === 'collection' ? 'Collections' : 'System')}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm mt-1 leading-relaxed">
                      {notification.message.includes(': ')
                        ? notification.message.split(': ')[1]
                        : notification.message}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span className="text-gray-400 text-sm flex-shrink-0">
                      {notification.time}
                    </span>
                    {!notification.isRead && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    )}
                    <button
                      onClick={(e) => deleteNotification(notification.id, e)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-red-100 rounded-full"
                      title="Delete notification"
                    >
                      <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredNotifications.length === 0 && (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h10v-1a3 3 0 00-3-3H7a3 3 0 00-3 3v1zM6 10a3 3 0 116 0 3 3 0 01-6 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {activeTab === 'all' ? 'No notifications yet' : 'No mentions yet'}
            </h3>
            <p className="text-gray-500 max-w-sm mx-auto">
              {activeTab === 'all'
                ? "You're all caught up! New notifications will appear here when you receive them."
                : "No one has mentioned you in comments yet. When they do, you'll see them here."
              }
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
