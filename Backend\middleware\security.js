const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');

/**
 * Newzora 安全中间件配置
 * 包含CSP、安全头、速率限制等安全措施
 */

// 环境配置
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';
const disableRateLimit = process.env.DISABLE_RATE_LIMIT === 'true' || isDevelopment;
const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

/**
 * Content Security Policy (CSP) 配置
 */
const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    
    // 脚本源 - 允许自身、内联脚本（开发环境）、CDN
    scriptSrc: [
      "'self'",
      ...(isDevelopment ? ["'unsafe-inline'", "'unsafe-eval'"] : []),
      'https://cdn.jsdelivr.net',
      'https://unpkg.com',
      'https://cdnjs.cloudflare.com',
      // Google Analytics (如果使用)
      'https://www.google-analytics.com',
      'https://www.googletagmanager.com',
    ],
    
    // 样式源 - 允许自身、内联样式、字体CDN
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Tailwind CSS需要
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net',
      'https://unpkg.com',
    ],
    
    // 图片源 - 允许自身、数据URL、外部图片源
    imgSrc: [
      "'self'",
      'data:',
      'blob:',
      'https:',
      // 如果使用外部图片服务
      'https://images.unsplash.com',
      'https://via.placeholder.com',
    ],
    
    // 字体源
    fontSrc: [
      "'self'",
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net',
    ],
    
    // 连接源 - API和WebSocket
    connectSrc: [
      "'self'",
      frontendUrl,
      'ws://localhost:*',
      'wss://localhost:*',
      ...(isDevelopment ? ['http://localhost:*', 'ws://localhost:*'] : []),
      // 如果使用外部API
      'https://api.github.com',
    ],
    
    // 媒体源
    mediaSrc: ["'self'", 'blob:', 'data:'],
    
    // 对象源
    objectSrc: ["'none'"],
    
    // 基础URI
    baseUri: ["'self'"],
    
    // 表单提交目标
    formAction: ["'self'"],
    
    // 框架祖先 - 防止点击劫持
    frameAncestors: ["'none'"],
    
    // 升级不安全请求（生产环境）
    ...(isProduction && { upgradeInsecureRequests: [] }),
  },
  
  // 报告违规行为
  reportOnly: isDevelopment,
  
  // 违规报告URI
  ...(isProduction && {
    reportUri: '/api/security/csp-report'
  }),
};

/**
 * 速率限制配置
 */
const createRateLimit = (windowMs, max, message, skipSuccessfulRequests = false) => {
  // 如果禁用速率限制，返回一个空的中间件
  if (disableRateLimit) {
    return (_req, _res, next) => next();
  }

  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      retryAfter: Math.ceil(windowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    // 跳过本地开发环境
    skip: (req) => isDevelopment && req.ip === '127.0.0.1',
  });
};

/**
 * 速率限制中间件
 */
const rateLimiters = {
  // 全局速率限制
  global: createRateLimit(
    15 * 60 * 1000, // 15分钟
    isProduction ? 1000 : 10000, // 生产环境1000次，开发环境10000次
    '请求过于频繁，请稍后再试'
  ),
  
  // 认证相关API严格限制 - 测试期间不限制
  auth: createRateLimit(
    15 * 60 * 1000, // 15分钟
    999999, // 测试期间不限制次数
    '登录尝试过于频繁，请15分钟后再试',
    true // 跳过成功的请求
  ),

  // 注册限制 - 测试期间不限制
  register: createRateLimit(
    60 * 60 * 1000, // 1小时
    999999, // 测试期间不限制次数
    '注册尝试过于频繁，请1小时后再试'
  ),
  
  // 密码重置限制
  passwordReset: createRateLimit(
    60 * 60 * 1000, // 1小时
    isProduction ? 3 : 30, // 生产环境3次，开发环境30次
    '密码重置请求过于频繁，请1小时后再试'
  ),
  
  // API调用限制
  api: createRateLimit(
    15 * 60 * 1000, // 15分钟
    isProduction ? 500 : 5000, // 生产环境500次，开发环境5000次
    'API调用过于频繁，请稍后再试'
  ),
};

/**
 * 慢速响应中间件 - 逐渐降低响应速度
 */
const slowDownMiddleware = slowDown({
  windowMs: 15 * 60 * 1000, // 15分钟
  delayAfter: isProduction ? 100 : 99999, // 超过100次请求后开始延迟，开发环境基本不延迟
  delayMs: 500, // 每次增加500ms延迟
  maxDelayMs: 20000, // 最大延迟20秒
  skip: () => isDevelopment, // 开发环境完全跳过慢速响应
});

/**
 * 安全头配置
 */
const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: cspConfig,
  
  // DNS预取控制
  dnsPrefetchControl: { allow: false },
  
  // 框架选项 - 防止点击劫持
  frameguard: { action: 'deny' },
  
  // 隐藏X-Powered-By头
  hidePoweredBy: true,
  
  // HTTP严格传输安全
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true,
  },
  
  // IE无嗅探
  ieNoOpen: true,
  
  // MIME类型嗅探防护
  noSniff: true,
  
  // 来源策略
  originAgentCluster: true,
  
  // 权限策略
  permittedCrossDomainPolicies: false,
  
  // 引用者策略
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  
  // XSS过滤器
  xssFilter: true,
});

/**
 * 输入验证中间件
 */
const inputValidation = (req, res, next) => {
  // 检查请求体大小
  const contentLength = parseInt(req.get('content-length') || '0');
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (contentLength > maxSize) {
    return res.status(413).json({
      success: false,
      message: '请求体过大',
      maxSize: '10MB',
    });
  }
  
  // 检查危险字符
  const dangerousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /onload\s*=/gi,
    /onerror\s*=/gi,
    /onclick\s*=/gi,
  ];
  
  const checkForDangerousContent = (obj, path = '') => {
    if (typeof obj === 'string') {
      for (const pattern of dangerousPatterns) {
        if (pattern.test(obj)) {
          throw new Error(`Potential XSS attack detected: ${path}`);
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        checkForDangerousContent(value, path ? `${path}.${key}` : key);
      }
    }
  };
  
  try {
    if (req.body) {
      checkForDangerousContent(req.body, 'body');
    }
    if (req.query) {
      checkForDangerousContent(req.query, 'query');
    }
    if (req.params) {
      checkForDangerousContent(req.params, 'params');
    }
  } catch (error) {
    return res.status(400).json({
      success: false,
      message: 'Input contains unsafe content',
      error: error.message,
    });
  }
  
  next();
};

/**
 * CSP违规报告处理
 */
const cspReportHandler = (req, res) => {
  console.warn('CSP Violation Report:', {
    timestamp: new Date().toISOString(),
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    report: req.body,
  });
  
  // 在生产环境中，可以将报告发送到监控系统
  if (isProduction) {
    // TODO: 发送到监控系统 (如Sentry)
  }
  
  res.status(204).end();
};

/**
 * 安全中间件导出
 */
module.exports = {
  // 基础安全头
  securityHeaders,
  
  // 速率限制
  rateLimiters,
  
  // 慢速响应
  slowDownMiddleware,
  
  // 输入验证
  inputValidation,
  
  // CSP报告处理
  cspReportHandler,
  
  // 应用所有安全中间件的便捷函数
  applySecurityMiddleware: (app) => {
    // 基础安全头
    app.use(securityHeaders);
    
    // 全局速率限制
    app.use(rateLimiters.global);
    
    // 慢速响应
    app.use(slowDownMiddleware);
    
    // 输入验证
    app.use(inputValidation);
    
    // CSP违规报告端点
    app.post('/api/security/csp-report', cspReportHandler);
    
    console.log('✅ 安全中间件已应用');
  },
};
