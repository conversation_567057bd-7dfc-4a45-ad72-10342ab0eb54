'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface PageState {
  returnUrl?: string;
  scrollPosition?: number;
  commentSectionId?: string;
  shouldScrollToComments?: boolean;
}

interface PageStateContextType {
  pageState: PageState;
  setReturnUrl: (url: string) => void;
  setScrollPosition: (position: number) => void;
  setCommentSectionTarget: (id: string) => void;
  setShouldScrollToComments: (should: boolean) => void;
  clearPageState: () => void;
  getReturnUrl: () => string | undefined;
  shouldScrollToCommentsOnLoad: () => boolean;
}

const PageStateContext = createContext<PageStateContextType | undefined>(undefined);

interface PageStateProviderProps {
  children: ReactNode;
}

export function PageStateProvider({ children }: PageStateProviderProps) {
  const [pageState, setPageState] = useState<PageState>({});

  const setReturnUrl = useCallback((url: string) => {
    setPageState(prev => ({ ...prev, returnUrl: url }));
  }, []);

  const setScrollPosition = useCallback((position: number) => {
    setPageState(prev => ({ ...prev, scrollPosition: position }));
  }, []);

  const setCommentSectionTarget = useCallback((id: string) => {
    setPageState(prev => ({ ...prev, commentSectionId: id }));
  }, []);

  const setShouldScrollToComments = useCallback((should: boolean) => {
    setPageState(prev => ({ ...prev, shouldScrollToComments: should }));
  }, []);

  const clearPageState = useCallback(() => {
    setPageState({});
  }, []);

  const getReturnUrl = useCallback(() => {
    return pageState.returnUrl;
  }, [pageState.returnUrl]);

  const shouldScrollToCommentsOnLoad = useCallback(() => {
    return pageState.shouldScrollToComments === true;
  }, [pageState.shouldScrollToComments]);

  const value: PageStateContextType = {
    pageState,
    setReturnUrl,
    setScrollPosition,
    setCommentSectionTarget,
    setShouldScrollToComments,
    clearPageState,
    getReturnUrl,
    shouldScrollToCommentsOnLoad
  };

  return (
    <PageStateContext.Provider value={value}>
      {children}
    </PageStateContext.Provider>
  );
}

export function usePageState() {
  const context = useContext(PageStateContext);
  if (context === undefined) {
    throw new Error('usePageState must be used within a PageStateProvider');
  }
  return context;
}
