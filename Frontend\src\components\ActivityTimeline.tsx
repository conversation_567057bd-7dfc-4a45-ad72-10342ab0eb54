'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ActivityMetadata {
  articleTitle?: string;
  followedUsername?: string;
  platform?: string;
  commentText?: string;
}

interface Activity {
  id: number;
  activityType: 'article_published' | 'article_liked' | 'article_commented' | 'user_followed' | 'article_shared';
  targetType: 'article' | 'user' | 'comment';
  targetId: number;
  metadata: ActivityMetadata;
  createdAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
}

interface ActivityTimelineProps {
  userId?: number;
  limit?: number;
  showHeader?: boolean;
}

export default function ActivityTimeline({
  userId,
  limit = 20,
  showHeader = true
}: ActivityTimelineProps) {
  const { user, token } = useAuth();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchActivities = async (pageNum: number = 1) => {
    setIsLoading(true);
    try {
      const endpoint = userId 
        ? `http://localhost:5000/api/activities/user/${userId}`
        : 'http://localhost:5000/api/activities/timeline';
      
      const response = await fetch(`${endpoint}?page=${pageNum}&limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const newActivities = data.data.activities || [];
        
        if (pageNum === 1) {
          setActivities(newActivities);
        } else {
          setActivities(prev => [...prev, ...newActivities]);
        }
        
        setHasMore(newActivities.length === limit);
        setPage(pageNum);
      } else {
        console.error('Failed to fetch activities');
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user && token) {
      fetchActivities(1);
    }
  }, [user, token, userId, limit, fetchActivities]);

  const loadMore = () => {
    if (!isLoading && hasMore) {
      fetchActivities(page + 1);
    }
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'article_published':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        );
      case 'article_liked':
        return (
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </div>
        );
      case 'article_commented':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        );
      case 'user_followed':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
        );
      case 'article_shared':
        return (
          <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getActivityText = (activity: Activity) => {
    const { activityType, metadata, user: activityUser } = activity;
    
    switch (activityType) {
      case 'article_published':
        return (
          <span>
            <strong>{activityUser.username}</strong> published a new article{' '}
            {metadata?.articleTitle && (
              <span className="text-blue-600 font-medium">&quot;{metadata.articleTitle}&quot;</span>
            )}
          </span>
        );
      case 'article_liked':
        return (
          <span>
            <strong>{activityUser.username}</strong> liked an article{' '}
            {metadata?.articleTitle && (
              <span className="text-blue-600 font-medium">&quot;{metadata.articleTitle}&quot;</span>
            )}
          </span>
        );
      case 'article_commented':
        return (
          <span>
            <strong>{activityUser.username}</strong> commented on{' '}
            {metadata?.articleTitle && (
              <span className="text-blue-600 font-medium">&quot;{metadata.articleTitle}&quot;</span>
            )}
          </span>
        );
      case 'user_followed':
        return (
          <span>
            <strong>{activityUser.username}</strong> started following{' '}
            {metadata?.followedUsername && (
              <span className="text-blue-600 font-medium">{metadata.followedUsername}</span>
            )}
          </span>
        );
      case 'article_shared':
        return (
          <span>
            <strong>{activityUser.username}</strong> shared an article{' '}
            {metadata?.articleTitle && (
              <span className="text-blue-600 font-medium">&quot;{metadata.articleTitle}&quot;</span>
            )}
            {metadata?.platform && (
              <span className="text-gray-500"> on {metadata.platform}</span>
            )}
          </span>
        );
      default:
        return (
          <span>
            <strong>{activityUser.username}</strong> performed an action
          </span>
        );
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)}m ago`;
    
    const diffInHours = diffInMinutes / 60;
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`;
    
    const diffInDays = diffInHours / 24;
    if (diffInDays < 7) return `${Math.floor(diffInDays)}d ago`;
    
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please login to view activity timeline</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {showHeader && (
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">
            {userId ? 'User Activity' : 'Activity Timeline'}
          </h2>
          <button
            onClick={() => fetchActivities(1)}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            disabled={isLoading}
          >
            Refresh
          </button>
        </div>
      )}

      <div className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex space-x-3 p-4 bg-white rounded-lg border border-gray-200">
            {getActivityIcon(activity.activityType)}
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900">
                {getActivityText(activity)}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {formatTime(activity.createdAt)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}

      {!isLoading && activities.length === 0 && (
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No activities yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            {userId ? 'This user has no recent activities.' : 'Start following users to see their activities here.'}
          </p>
        </div>
      )}

      {!isLoading && hasMore && activities.length > 0 && (
        <div className="text-center">
          <button
            onClick={loadMore}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
}
