'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';

interface SimpleArticle {
  id: number;
  title: string;
  content: string;
  author: {
    id: number;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
  } | string;
  publishedAt?: string;
  createdAt: string;
  readTime: number;
  views: number;
  likes: number;
  comments?: number;
  shares?: number;
}

export default function SimpleArticlePage() {
  const params = useParams();
  const router = useRouter();
  const [article, setArticle] = useState<SimpleArticle | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (params.id) {
      fetchArticle(params.id as string);
    }
  }, [params.id]);

  const fetchArticle = async (id: string) => {
    try {
      console.log('Fetching article:', id);
      setLoading(true);
      setError(null);
      
      const response = await fetch(`http://localhost:5000/api/articles/${id}`);
      console.log('Response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Article data:', data);
        setArticle(data);
      } else {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        setError(`Failed to load article: ${response.status}`);
      }
    } catch (error) {
      console.error('Network error:', error);
      setError(`Network error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded mb-4"></div>
              <div className="h-64 bg-gray-300 rounded mb-6"></div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-8">
            <h1 className="text-2xl font-bold text-red-900 mb-4">Error Loading Article</h1>
            <p className="text-red-700 mb-4">{error}</p>
            <button
              onClick={() => fetchArticle(params.id as string)}
              className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8">
            <h1 className="text-2xl font-bold text-yellow-900 mb-4">Article Not Found</h1>
            <p className="text-yellow-700">The requested article could not be found.</p>
          </div>
        </div>
      </div>
    );
  }

  // Safe author extraction
  const authorName = typeof article.author === 'object' ? article.author.name : (article.author || 'Unknown Author');
  const authorAvatar = typeof article.author === 'object' ? article.author.avatar : `https://ui-avatars.com/api/?name=${encodeURIComponent(authorName)}&background=6366f1&color=fff&size=48`;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Breadcrumb Navigation */}
      <nav className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push('/explore')}
            className="hover:text-blue-600 transition-colors"
          >
            Articles
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {article.title.length > 50 ? article.title.substring(0, 50) + '...' : article.title}
          </span>
        </div>
      </nav>

      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          {/* Debug Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-bold text-blue-900 mb-2">🔍 Debug Information</h3>
            <p className="text-blue-700 text-sm">
              <strong>Article ID:</strong> {article.id}<br/>
              <strong>Title:</strong> {article.title}<br/>
              <strong>Author Type:</strong> {typeof article.author}<br/>
              <strong>Author Name:</strong> {authorName}<br/>
              <strong>Content Length:</strong> {article.content?.length || 0} characters<br/>
              <strong>Published:</strong> {article.publishedAt || article.createdAt}<br/>
            </p>
          </div>

          {/* Article Title */}
          <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">
            {article.title}
          </h1>

          {/* Author Info */}
          <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <img
              src={authorAvatar}
              alt={authorName}
              className="w-12 h-12 rounded-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=Author&background=6366f1&color=fff&size=48`;
              }}
            />
            <div>
              <h3 className="font-semibold text-gray-900">
                {authorName}
              </h3>
              <p className="text-sm text-gray-500">
                Published {new Date(article.publishedAt || article.createdAt).toLocaleDateString()} • {article.readTime} min read
              </p>
            </div>
          </div>

          {/* Article Stats */}
          <div className="mb-8 flex items-center space-x-6 text-sm text-gray-500">
            <span>👁️ {article.views || 0} views</span>
            <span>❤️ {article.likes || 0} likes</span>
            <span>💬 {article.comments || 0} comments</span>
            <span>📤 {article.shares || 0} shares</span>
          </div>

          {/* Article Content */}
          <div className="prose max-w-none">
            <div dangerouslySetInnerHTML={{ __html: article.content }} />
          </div>

          {/* Test Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="font-bold text-gray-900 mb-4">🧪 Test Actions</h3>
            <div className="space-x-4">
              <button
                onClick={() => fetchArticle(params.id as string)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Reload Article
              </button>
              <button
                onClick={() => window.open('/article/1', '_blank')}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Open Original Page
              </button>
              <button
                onClick={() => console.log('Article data:', article)}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Log to Console
              </button>
            </div>
          </div>
        </div>
      </article>
    </div>
  );
}
