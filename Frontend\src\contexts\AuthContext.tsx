'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// 用户类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  name?: string;
}

// 认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  loading: boolean; // 添加 loading 别名以保持兼容性
  error: string;
}

// 认证操作
interface AuthActions {
  login: (email: string, password: string) => Promise<boolean>;
  register: (username: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  setError: (error: string) => void;
  clearError: () => void;
  updateUser: (userData: Partial<User>) => void;
  resetPassword: (email: string) => Promise<boolean>;
  refreshAuthState: () => void;
}

// Context类型
type AuthContextType = AuthState & AuthActions;

// 创建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API URL
const API_URL = 'http://localhost:5000/api';

// AuthProvider组件
export function AuthProvider({ children }: { children: ReactNode }) {
  // 立即从 localStorage 初始化状态，避免闪烁
  const [user, setUser] = useState<User | null>(() => {
    if (typeof window !== 'undefined') {
      try {
        const storedUser = localStorage.getItem('auth_user');
        const userData = storedUser ? JSON.parse(storedUser) : null;
        console.log('🔄 AuthContext初始化 - 从localStorage恢复用户:', userData?.username || '无');
        return userData;
      } catch (error) {
        console.error('❌ 解析localStorage用户数据失败:', error);
        return null;
      }
    }
    return null;
  });

  const [token, setToken] = useState<string | null>(() => {
    if (typeof window !== 'undefined') {
      const storedToken = localStorage.getItem('auth_token');
      console.log('🔄 AuthContext初始化 - 从localStorage恢复token:', storedToken ? '存在' : '不存在');
      return storedToken;
    }
    return null;
  });

  const [error, setError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false); // 改为 false，因为已经从 localStorage 初始化了

  // 计算认证状态
  const isAuthenticated = !!(user && token);

  // 调试认证状态变化
  React.useEffect(() => {
    console.log('🔍 AuthContext状态变化:', {
      hasUser: !!user,
      hasToken: !!token,
      isAuthenticated,
      username: user?.username,
      tokenPreview: token ? token.substring(0, 20) + '...' : '无',
      timestamp: new Date().toLocaleTimeString()
    });

    // 检查 localStorage 中的数据
    const storedToken = localStorage.getItem('auth_token');
    const storedUser = localStorage.getItem('auth_user');
    console.log('🔍 localStorage状态:', {
      hasStoredToken: !!storedToken,
      hasStoredUser: !!storedUser,
      storedUsername: storedUser ? JSON.parse(storedUser)?.username : '无'
    });

    // 增强：检测并处理认证状态不一致问题
    if (isAuthenticated) {
      // 已认证状态下，设置一个会话级别的标记，防止在登录后被错误重定向
      sessionStorage.setItem('authenticated_session', 'true');

      // 检查当前URL是否为登录页，如果是则跳转到首页
      if (typeof window !== 'undefined' && window.location.pathname === '/login') {
        console.log('⚠️ 检测到已认证用户在登录页面，重定向到首页');
        // 使用直接跳转而非router避免路由冲突
        window.location.href = '/';
      }
    } else {
      // 未认证状态，清除会话标记
      sessionStorage.removeItem('authenticated_session');
    }
  }, [user, token, isAuthenticated]);

  // 登录函数
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      console.log('🔐 开始登录:', email);

      // 登录前清除任何可能的旧认证数据，确保状态一致性
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth_user');
      sessionStorage.removeItem('authenticated_session');

      // 确保使用最新的认证状态
      setUser(null);
      setToken(null);

      const response = await fetch(`${API_URL}/users/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // 关键：允许发送和接收Cookie
        body: JSON.stringify({ identifier: email, password }),
      });

      const data = await response.json();
      console.log('📡 登录响应:', data);

      if (data.success && data.user && data.token) {
        console.log('✅ 登录成功，用户:', data.user.username);
        console.log('🔑 Token:', data.token.substring(0, 20) + '...');

        // 🔥 关键修改：同步更新所有状态，配合登录页面的修改
        await new Promise(resolve => {
          // 1. 先保存到 localStorage
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('auth_user', JSON.stringify(data.user));
          localStorage.setItem('login_timestamp', String(Date.now()));
          localStorage.setItem('last_successful_login', String(Date.now()));
          localStorage.setItem('authenticated', 'true');

          // 2. 设置会话标记，配合登录页面的检查逻辑
          sessionStorage.setItem('auth_verified', 'true');
          sessionStorage.setItem('authenticated_session', 'true');
          sessionStorage.setItem('login_success', 'true');
          sessionStorage.setItem('auth_source', 'direct_login');

          // 3. 更新 React 状态
          setUser(data.user);
          setToken(data.token);

          // 4. 等待状态同步完成
          setTimeout(resolve, 100);
        });

        console.log('🔄 AuthContext状态已同步完成');

        // 强制触发状态更新事件，确保所有组件都能收到更新
        window.dispatchEvent(new Event('auth-state-changed'));

        // 延迟导入socketService，确保在认证状态完全设置后才尝试连接
        setTimeout(async () => {
          try {
            // 动态导入socketService，避免循环依赖
            const socketModule = await import('@/services/socketService');
            const socketService = socketModule.default;

            // 尝试建立WebSocket连接，但不依赖其成功
            if (data.token) {
              console.log('🔌 尝试建立WebSocket连接');
              socketService.connect(data.token).catch(err => {
                console.warn('WebSocket连接警告 (非致命):', err);
                // WebSocket连接失败不影响登录状态
              });
            }
          } catch (err) {
            console.warn('WebSocket模块加载警告 (非致命):', err);
          }
        }, 500);

        return true;
      } else {
        console.log('❌ 登录失败:', data.message);
        setError(data.message || '登录失败');
        return false;
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('Network error, please try again');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setError('');
      console.log('📝 Starting registration:', email);

      const response = await fetch(`${API_URL}/users/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include', // 允许发送和接收Cookie
        body: JSON.stringify({ username, email, password }),
      });

      const data = await response.json();
      console.log('📡 Registration response:', data);

      if (data.success) {
        console.log('✅ Registration successful');
        return true;
      } else {
        setError(data.message || 'Registration failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Registration error:', error);
      setError('Network error, please try again');
      return false;
    }
  };

  // Helper function to clear cookie
  const clearCookie = (name: string) => {
    if (typeof document !== 'undefined') {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    }
  };

  // 登出函数
  const logout = async () => {
    console.log('🚪 用户登出');

    try {
      // 调用后端logout端点清除服务器端Cookie
      await fetch(`${API_URL}/users/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('❌ 后端登出失败:', error);
    }

    setUser(null);
    setToken(null);
    setError('');

    // 清除localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');

    // 清除cookies（前端能访问的）
    clearCookie('user_role');

    console.log('🗑️ 已清除所有认证信息');
  };

  // 清除错误
  const clearError = () => {
    setError('');
  };

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('auth_user', JSON.stringify(updatedUser));
    }
  };

  // 重置密码
  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');

      const response = await fetch(`${API_URL}/auth/forgot-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return true;
      } else {
        setError(data.message || 'Password reset failed');
        return false;
      }
    } catch (error) {
      setError('Network error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 监听 localStorage 变化，用于多标签页同步
  React.useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' || e.key === 'auth_user') {
        const storedToken = localStorage.getItem('auth_token');
        const storedUser = localStorage.getItem('auth_user');

        if (storedToken && storedUser) {
          try {
            const userData = JSON.parse(storedUser);
            setUser(userData);
            setToken(storedToken);
            console.log('✅ 从localStorage同步认证状态:', userData.username);
          } catch (error) {
            console.error('❌ 同步认证状态失败:', error);
            setUser(null);
            setToken(null);
          }
        } else {
          setUser(null);
          setToken(null);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // 添加强制刷新认证状态的方法
  const refreshAuthState = React.useCallback(() => {
    console.log('🔄 强制刷新认证状态');
    const storedToken = localStorage.getItem('auth_token');
    const storedUser = localStorage.getItem('auth_user');

    if (storedToken && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setToken(storedToken);
        console.log('✅ 强制刷新成功:', userData.username);
      } catch (error) {
        console.error('❌ 强制刷新失败:', error);
        setUser(null);
        setToken(null);
      }
    } else {
      setUser(null);
      setToken(null);
    }
  }, []);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    login,
    register,
    logout,
    error,
    setError,
    clearError,
    updateUser,
    resetPassword,
    refreshAuthState,
    isLoading,
    loading: isLoading, // 添加 loading 别名以保持兼容性
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// useAuth hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}