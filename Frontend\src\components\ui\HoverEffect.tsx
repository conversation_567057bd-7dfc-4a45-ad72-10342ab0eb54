'use client';

import React, { ReactNode, useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export interface HoverEffectProps {
  children: ReactNode;
  effect?: 'lift' | 'glow' | 'tilt' | 'scale' | 'magnetic' | 'ripple';
  intensity?: 'subtle' | 'medium' | 'strong';
  className?: string;
  disabled?: boolean;
}

export function HoverEffect({
  children,
  effect = 'lift',
  intensity = 'medium',
  className,
  disabled = false
}: HoverEffectProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const elementRef = useRef<HTMLDivElement>(null);

  // 磁性效果的鼠标跟踪
  useEffect(() => {
    if (effect !== 'magnetic' || disabled) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!elementRef.current) return;

      const rect = elementRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      setMousePosition({
        x: (e.clientX - centerX) * 0.1,
        y: (e.clientY - centerY) * 0.1
      });
    };

    if (isHovered) {
      document.addEventListener('mousemove', handleMouseMove);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isHovered, effect, disabled]);

  // 强度配置
  const intensityConfig = {
    subtle: {
      lift: 'hover:-translate-y-1 hover:shadow-md',
      glow: 'hover:shadow-lg hover:shadow-primary/20',
      tilt: 'hover:rotate-1',
      scale: 'hover:scale-[1.02]',
      magnetic: 0.05,
      ripple: 'active:scale-95'
    },
    medium: {
      lift: 'hover:-translate-y-2 hover:shadow-lg',
      glow: 'hover:shadow-xl hover:shadow-primary/30',
      tilt: 'hover:rotate-2',
      scale: 'hover:scale-105',
      magnetic: 0.1,
      ripple: 'active:scale-90'
    },
    strong: {
      lift: 'hover:-translate-y-4 hover:shadow-2xl',
      glow: 'hover:shadow-2xl hover:shadow-primary/40',
      tilt: 'hover:rotate-3',
      scale: 'hover:scale-110',
      magnetic: 0.15,
      ripple: 'active:scale-85'
    }
  };

  // 获取效果样式
  const getEffectStyles = () => {
    if (disabled) return '';

    const config = intensityConfig[intensity];
    
    switch (effect) {
      case 'lift':
        return config.lift;
      case 'glow':
        return config.glow;
      case 'tilt':
        return `${config.tilt} hover:scale-105`;
      case 'scale':
        return config.scale;
      case 'magnetic':
        return '';
      case 'ripple':
        return config.ripple;
      default:
        return '';
    }
  };

  // 磁性效果的内联样式
  const getMagneticStyles = () => {
    if (effect !== 'magnetic' || disabled) return {};

    return {
      transform: isHovered 
        ? `translate(${mousePosition.x}px, ${mousePosition.y}px)`
        : 'translate(0px, 0px)',
      transition: isHovered ? 'transform 0.1s ease-out' : 'transform 0.3s ease-out'
    };
  };

  const baseStyles = cn(
    'transition-all duration-300 ease-out',
    getEffectStyles(),
    className
  );

  return (
    <div
      ref={elementRef}
      className={baseStyles}
      style={getMagneticStyles()}
      onMouseEnter={() => !disabled && setIsHovered(true)}
      onMouseLeave={() => !disabled && setIsHovered(false)}
    >
      {children}
    </div>
  );
}

// 涟漪效果组件
export function RippleEffect({
  children,
  color = 'rgba(255, 255, 255, 0.6)',
  duration = 600,
  className
}: {
  children: ReactNode;
  color?: string;
  duration?: number;
  className?: string;
}) {
  const [ripples, setRipples] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
  }>>([]);

  const addRipple = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const newRipple = {
      id: Date.now(),
      x,
      y,
      size
    };

    setRipples(prev => [...prev, newRipple]);

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, duration);
  };

  return (
    <div
      className={cn('relative overflow-hidden', className)}
      onMouseDown={addRipple}
    >
      {children}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute rounded-full pointer-events-none animate-ping"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            backgroundColor: color,
            animationDuration: `${duration}ms`
          }}
        />
      ))}
    </div>
  );
}

// 悬停显示组件
export function HoverReveal({
  children,
  hoverContent,
  direction = 'up',
  className
}: {
  children: ReactNode;
  hoverContent: ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
}) {
  const [isHovered, setIsHovered] = useState(false);

  const directionStyles = {
    up: 'bottom-full mb-2',
    down: 'top-full mt-2',
    left: 'right-full mr-2',
    right: 'left-full ml-2'
  };

  return (
    <div
      className={cn('relative inline-block', className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
      
      {isHovered && (
        <div className={cn(
          'absolute z-50 animate-fade-in',
          directionStyles[direction]
        )}>
          {hoverContent}
        </div>
      )}
    </div>
  );
}

// 3D翻转卡片效果
export function FlipCard({
  frontContent,
  backContent,
  className
}: {
  frontContent: ReactNode;
  backContent: ReactNode;
  className?: string;
}) {
  const [isFlipped, setIsFlipped] = useState(false);

  return (
    <div
      className={cn('relative w-full h-full perspective-1000', className)}
      onMouseEnter={() => setIsFlipped(true)}
      onMouseLeave={() => setIsFlipped(false)}
    >
      <div className={cn(
        'relative w-full h-full transition-transform duration-600 transform-style-preserve-3d',
        isFlipped && 'rotate-y-180'
      )}>
        {/* 正面 */}
        <div className="absolute inset-0 w-full h-full backface-hidden">
          {frontContent}
        </div>
        
        {/* 背面 */}
        <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180">
          {backContent}
        </div>
      </div>
    </div>
  );
}
