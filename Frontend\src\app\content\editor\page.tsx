'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Link from 'next/link';
import {
  PhotoIcon,
  BookmarkIcon,
  EyeIcon,
  ArrowLeftIcon,
  TrashIcon
} from '@heroicons/react/24/outline';

const API_BASE_URL = 'http://localhost:5000/api';

interface Draft {
  id?: number;
  title: string;
  content: string;
  category: string;
  status: 'draft' | 'auto_saved' | 'ready_for_review';
  tags: string[];
  metadata: {
    wordCount: number;
    readingTime: number;
    lastSaved: string;
  };
}



export default function EditorPage() {
  const { token, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const draftId = searchParams.get('id');

  const [draft, setDraft] = useState<Draft>({
    title: '',
    content: '',
    category: 'general',
    status: 'draft',
    tags: [],
    metadata: {
      wordCount: 0,
      readingTime: 0,
      lastSaved: ''
    }
  });


  const [saving, setSaving] = useState(false);
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Define functions first
  const loadDraft = useCallback(async (id: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/drafts/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDraft(data.data);
      } else {
        console.error('Failed to load draft');
      }
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  }, [token]);

  const handleSave = useCallback(async (status: 'draft' | 'auto_saved' | 'ready_for_review' = 'draft') => {
    if (!draft.title.trim()) {
      alert('Please enter article title');
      return;
    }

    try {
      setSaving(true);
      const method = draftId ? 'PUT' : 'POST';
      const url = draftId ? `${API_BASE_URL}/drafts/${draftId}` : `${API_BASE_URL}/drafts`;

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...draft,
          status
        })
      });

      if (response.ok) {
        const data = await response.json();
        setDraft(data.data);
        setHasUnsavedChanges(false);

        if (status === 'ready_for_review') {
          alert('Article submitted for review successfully!');
          router.push('/content/drafts');
        } else if (status === 'draft') {
          alert('Draft saved successfully!');
          if (!draftId) {
            router.push(`/content/editor?draftId=${data.data.id}`);
          }
        }
      } else {
        alert('Save failed, please try again');
      }
    } catch (error) {
      console.error('Save error:', error);
      alert('Save failed, please try again');
    } finally {
      setSaving(false);
    }
  }, [draft, token, router, draftId]);

  // Load draft data
  useEffect(() => {
    // 等待认证状态加载完成
    if (isLoading) return;

    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    if (draftId) {
      loadDraft(parseInt(draftId));
    }
  }, [isAuthenticated, isLoading, draftId, router, loadDraft]);

  // Auto save
  useEffect(() => {
    if (!autoSaveEnabled || !hasUnsavedChanges || !draft.title) return;

    const autoSaveTimer = setTimeout(() => {
      handleSave('auto_saved');
    }, 30000); // Auto save every 30 seconds

    return () => clearTimeout(autoSaveTimer);
  }, [draft, autoSaveEnabled, hasUnsavedChanges, handleSave]);

  // Calculate word count and reading time
  useEffect(() => {
    const wordCount = draft.content.split(/\s+/).filter(word => word.length > 0).length;
    const readingTime = Math.ceil(wordCount / 200); // Assume 200 words per minute

    setDraft(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        wordCount,
        readingTime
      }
    }));
  }, [draft.content]);

  const handleInputChange = (field: keyof Draft, value: string | string[]) => {
    setDraft(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
  };

  const handleTagAdd = (tag: string) => {
    if (tag.trim() && !draft.tags.includes(tag.trim())) {
      handleInputChange('tags', [...draft.tags, tag.trim()]);
    }
  };

  const handleTagRemove = (tagToRemove: string) => {
    handleInputChange('tags', draft.tags.filter(tag => tag !== tagToRemove));
  };

  if (!isAuthenticated) {
    return null;
  }

  if (saving) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Saving...</p>
          </div>
        </div>
      </div>
    );
  }





  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/content"
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeftIcon className="h-5 w-5 mr-2" />
              Back to Content Management
            </Link>
            <div className="h-6 border-l border-gray-300"></div>
            <h1 className="text-2xl font-bold text-gray-900">
              {draft.id ? 'Edit Article' : 'New Article'}
            </h1>
          </div>

          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-500">
              {hasUnsavedChanges ? 'Unsaved changes' : 'Saved'}
            </span>
            <button
              onClick={() => handleSave('draft')}
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <BookmarkIcon className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Draft'}
            </button>
            <button
              onClick={() => handleSave('ready_for_review')}
              disabled={saving || !draft.title.trim()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <EyeIcon className="h-4 w-4 mr-2" />
              Submit for Review
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main editing area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Title input */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Article Title *
              </label>
              <input
                type="text"
                id="title"
                value={draft.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter article title..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Content editor */}
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="border-b border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900">Article Content</h3>
                  <button
                    onClick={() => setShowMediaLibrary(true)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <PhotoIcon className="h-4 w-4 mr-2" />
                    Insert Media
                  </button>
                </div>
              </div>
              
              <div className="p-6">
                <textarea
                  value={draft.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Start writing your article content..."
                  rows={20}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Article information */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Article Information</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={draft.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="general">General</option>
                    <option value="technology">Technology</option>
                    <option value="business">Business</option>
                    <option value="sports">Sports</option>
                    <option value="entertainment">Entertainment</option>
                    <option value="health">Health</option>
                    <option value="politics">Politics</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {draft.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          onClick={() => handleTagRemove(tag)}
                          className="ml-1.5 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-500"
                        >
                          <TrashIcon className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    placeholder="Enter tag and press Enter"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleTagAdd(e.currentTarget.value);
                        e.currentTarget.value = '';
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* Statistics */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics</h3>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Word Count</span>
                  <span className="text-sm font-medium text-gray-900">
                    {draft.metadata.wordCount} words
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Reading Time</span>
                  <span className="text-sm font-medium text-gray-900">
                    {draft.metadata.readingTime} minutes
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {draft.status === 'draft' ? 'Draft' :
                     draft.status === 'auto_saved' ? 'Auto Saved' : 'Under Review'}
                  </span>
                </div>
                {draft.metadata.lastSaved && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Last Saved</span>
                    <span className="text-sm font-medium text-gray-900">
                      {new Date(draft.metadata.lastSaved).toLocaleString('en-US')}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Settings */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Editor Settings</h3>

              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={autoSaveEnabled}
                    onChange={(e) => setAutoSaveEnabled(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                  <span className="ml-2 text-sm text-gray-700">Enable Auto Save</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Media library modal */}
      {showMediaLibrary && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Select Media File</h3>
              <button
                onClick={() => setShowMediaLibrary(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="sr-only">Close</span>
                ✕
              </button>
            </div>

            <div className="text-center py-8">
              <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Media Library Under Development</h3>
              <p className="mt-1 text-sm text-gray-500">
                Media file selection feature will be implemented in future versions
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
