import { Work, Article, Video, Audio } from '@/types';

// 模拟文章数据
const mockArticles: Article[] = [
  {
    id: 1,
    type: 'article',
    title: 'The Future of Artificial Intelligence in 2024',
    description: 'Exploring the latest developments in AI technology and their impact on various industries.',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
    category: 'Technology',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
    author: {
      id: 1,
      name: '<PERSON>',
      username: 'alex<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Tech journalist and AI researcher',
      isFollowing: false
    },
    readTime: 8,
    tags: ['AI', 'Technology', 'Future'],
    views: 15420,
    likes: 892,
    comments: 156,
    shares: 234,
    bookmarks: 445,
    featured: true,
    published: true,
    publishedAt: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    type: 'article',
    title: 'Sustainable Travel: A Guide to Eco-Friendly Adventures',
    description: 'Discover how to explore the world while minimizing your environmental impact.',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
    category: 'Travel',
    image: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800&h=400&fit=crop',
    author: {
      id: 2,
      name: 'Sarah Johnson',
      username: 'sarahj',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=100&h=100&fit=crop&crop=face',
      bio: 'Travel blogger and environmental advocate',
      isFollowing: false
    },
    readTime: 6,
    tags: ['Travel', 'Sustainability', 'Environment'],
    views: 8750,
    likes: 567,
    comments: 89,
    shares: 123,
    bookmarks: 234,
    featured: false,
    published: true,
    publishedAt: '2024-01-14T14:20:00Z',
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-14T14:20:00Z'
  }
];

// 模拟视频数据
const mockVideos: Video[] = [
  {
    id: 3,
    type: 'video',
    title: 'Building a Modern Web Application with Next.js 14',
    description: 'A comprehensive tutorial on creating scalable web applications using the latest Next.js features.',
    category: 'Technology',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
    duration: 1845, // 30 minutes 45 seconds
    resolution: {
      width: 1920,
      height: 1080,
      quality: '1080p'
    },
    fileSize: *********, // 500MB
    author: {
      id: 3,
      name: 'Mike Rodriguez',
      username: 'mikedev',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Full-stack developer and educator',
      isFollowing: false
    },
    tags: ['Next.js', 'React', 'Web Development', 'Tutorial'],
    views: 23450,
    likes: 1234,
    comments: 287,
    shares: 156,
    bookmarks: 678,
    featured: true,
    published: true,
    publishedAt: '2024-01-13T09:15:00Z',
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z'
  },
  {
    id: 4,
    type: 'video',
    title: 'Cooking Authentic Italian Pasta from Scratch',
    description: 'Learn the traditional techniques for making perfect pasta dough and classic Italian sauces.',
    category: 'Food',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=800&h=400&fit=crop',
    duration: 1260, // 21 minutes
    resolution: {
      width: 2560,
      height: 1440,
      quality: '1440p'
    },
    fileSize: 1073741824, // 1GB
    author: {
      id: 4,
      name: 'Isabella Romano',
      username: 'chefisabella',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      bio: 'Professional chef and culinary instructor',
      isFollowing: false
    },
    tags: ['Cooking', 'Italian', 'Pasta', 'Recipe'],
    views: 45670,
    likes: 2890,
    comments: 456,
    shares: 234,
    bookmarks: 1234,
    featured: false,
    published: true,
    publishedAt: '2024-01-12T16:45:00Z',
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-12T16:45:00Z'
  },
  {
    id: 5,
    type: 'video',
    title: 'Exploring the Hidden Gems of Tokyo',
    description: 'A cinematic journey through Tokyo\'s lesser-known neighborhoods and local culture.',
    category: 'Travel',
    videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&h=400&fit=crop',
    duration: 2340, // 39 minutes
    resolution: {
      width: 3840,
      height: 2160,
      quality: '2160p'
    },
    fileSize: 2147483648, // 2GB
    author: {
      id: 5,
      name: 'Kenji Tanaka',
      username: 'kenjitravels',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Travel filmmaker and cultural explorer',
      isFollowing: false
    },
    tags: ['Travel', 'Tokyo', 'Japan', 'Culture', 'Documentary'],
    views: 67890,
    likes: 4567,
    comments: 789,
    shares: 456,
    bookmarks: 2345,
    featured: true,
    published: true,
    publishedAt: '2024-01-11T12:00:00Z',
    createdAt: '2024-01-11T12:00:00Z',
    updatedAt: '2024-01-11T12:00:00Z'
  }
];

// 模拟音频数据
const mockAudios: Audio[] = [
  {
    id: 6,
    type: 'audio',
    title: 'The Psychology of Success: A Deep Dive',
    description: 'An in-depth discussion about the mental frameworks that drive successful individuals.',
    category: 'Lifestyle',
    audioUrl: 'https://sample-audio.com/sample.mp3',
    coverUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',
    duration: 3600, // 1 hour
    fileSize: 86400000, // 82MB
    author: {
      id: 6,
      name: 'Dr. Emma Wilson',
      username: 'dremmaw',
      avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
      bio: 'Psychologist and bestselling author',
      isFollowing: false
    },
    tags: ['Psychology', 'Success', 'Mindset', 'Podcast'],
    views: 12340,
    likes: 789,
    comments: 123,
    shares: 67,
    bookmarks: 456,
    featured: false,
    published: true,
    publishedAt: '2024-01-10T08:30:00Z',
    createdAt: '2024-01-10T08:30:00Z',
    updatedAt: '2024-01-10T08:30:00Z'
  },
  {
    id: 7,
    type: 'audio',
    title: 'Financial Planning for Young Professionals',
    description: 'Essential financial advice for millennials and Gen Z entering the workforce.',
    category: 'Finance',
    audioUrl: 'https://sample-audio.com/sample2.mp3',
    coverUrl: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=400&fit=crop',
    duration: 2700, // 45 minutes
    fileSize: 64800000, // 62MB
    author: {
      id: 7,
      name: 'Robert Kim',
      username: 'robertfinance',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Financial advisor and investment strategist',
      isFollowing: false
    },
    tags: ['Finance', 'Investment', 'Career', 'Money'],
    views: 8900,
    likes: 567,
    comments: 89,
    shares: 45,
    bookmarks: 234,
    featured: false,
    published: true,
    publishedAt: '2024-01-09T15:20:00Z',
    createdAt: '2024-01-09T15:20:00Z',
    updatedAt: '2024-01-09T15:20:00Z'
  },
  {
    id: 8,
    type: 'audio',
    title: 'The History of Ancient Rome: Rise and Fall',
    description: 'A fascinating exploration of Roman civilization from its founding to its collapse.',
    category: 'History',
    audioUrl: 'https://sample-audio.com/sample3.mp3',
    coverUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=400&fit=crop',
    duration: 4500, // 1 hour 15 minutes
    fileSize: 108000000, // 103MB
    author: {
      id: 8,
      name: 'Prof. Marcus Thompson',
      username: 'profmarcus',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'History professor and author',
      isFollowing: false
    },
    tags: ['History', 'Rome', 'Ancient', 'Education'],
    views: 15670,
    likes: 1234,
    comments: 234,
    shares: 123,
    bookmarks: 567,
    featured: true,
    published: true,
    publishedAt: '2024-01-08T11:45:00Z',
    createdAt: '2024-01-08T11:45:00Z',
    updatedAt: '2024-01-08T11:45:00Z'
  }
];

// 合并所有作品
export const mockWorks: Work[] = [
  ...mockArticles,
  ...mockVideos,
  ...mockAudios
].sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime());

// 按类型分组的数据
export const mockWorksByType = {
  articles: mockArticles,
  videos: mockVideos,
  audios: mockAudios
};

// 按分类分组的数据
export const mockWorksByCategory = mockWorks.reduce((acc, work) => {
  const category = work.category;
  if (!acc[category]) {
    acc[category] = [];
  }
  acc[category].push(work);
  return acc;
}, {} as Record<string, Work[]>);

// 特色作品
export const featuredWorks = mockWorks.filter(work => work.featured);

// 热门作品（按观看量排序）
export const trendingWorks = [...mockWorks].sort((a, b) => b.views - a.views);

// 最新作品
export const latestWorks = [...mockWorks].sort((a, b) => 
  new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime()
);
