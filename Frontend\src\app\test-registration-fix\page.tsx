'use client';

import React, { useState } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

export default function TestRegistrationFixPage() {
  const { login, register, logout, user, isAuthenticated, error } = useSupabaseAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [testPassword, setTestPassword] = useState('');
  const [testUsername, setTestUsername] = useState('');

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 生成随机测试账户
  const generateTestAccount = () => {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 8);
    setTestEmail(`test${timestamp}@example.com`);
    setTestPassword('TestPassword123!');
    setTestUsername(`testuser${randomId}`);
  };

  // 测试完整的注册-登录流程
  const testFullFlow = async () => {
    setIsLoading(true);
    addResult('🚀 开始完整注册-登录流程测试');
    
    try {
      // 生成新的测试账户
      generateTestAccount();
      const email = `test${Date.now()}@example.com`;
      const password = 'TestPassword123!';
      const username = `testuser${Date.now()}`;
      
      addResult(`📝 测试账户: ${email}`);
      
      // 步骤1: 注册
      addResult('📝 步骤1: 开始注册...');
      const registerSuccess = await register(email, password, username, 'Test User');
      
      if (registerSuccess) {
        addResult('✅ 步骤1: 注册成功！');
        
        // 步骤2: 登出（如果已登录）
        if (isAuthenticated) {
          addResult('🚪 步骤2: 登出当前用户...');
          await logout();
          addResult('✅ 步骤2: 登出成功！');
        }
        
        // 等待一下确保状态更新
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 步骤3: 使用刚注册的账户登录
        addResult('🔐 步骤3: 使用新账户登录...');
        const loginSuccess = await login(email, password);
        
        if (loginSuccess) {
          addResult('✅ 步骤3: 登录成功！');
          addResult('🎉 完整流程测试通过！注册的账户可以正常登录');
        } else {
          addResult('❌ 步骤3: 登录失败！');
          addResult(`💥 错误信息: ${error}`);
          addResult('🔍 这表明注册的账户无法登录，存在问题');
        }
      } else {
        addResult('❌ 步骤1: 注册失败！');
        addResult(`💥 错误信息: ${error}`);
      }
    } catch (err) {
      addResult(`💥 测试异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试预置账户登录
  const testPresetAccounts = async () => {
    setIsLoading(true);
    addResult('🔐 测试预置账户登录');
    
    const presetAccounts = [
      { email: '<EMAIL>', password: 'TestPassword123!', name: 'Gmail账户' },
      { email: '<EMAIL>', password: 'TestPassword123!', name: 'Hotmail账户' }
    ];
    
    for (const account of presetAccounts) {
      try {
        addResult(`🔐 测试 ${account.name}: ${account.email}`);
        const success = await login(account.email, account.password);
        
        if (success) {
          addResult(`✅ ${account.name} 登录成功！`);
          await logout();
          addResult(`🚪 ${account.name} 登出成功！`);
        } else {
          addResult(`❌ ${account.name} 登录失败: ${error}`);
        }
      } catch (err) {
        addResult(`💥 ${account.name} 测试异常: ${err}`);
      }
      
      // 等待一下再测试下一个
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsLoading(false);
  };

  // 手动注册测试
  const testManualRegistration = async () => {
    if (!testEmail || !testPassword || !testUsername) {
      addResult('❌ 请填写完整的测试账户信息');
      return;
    }
    
    setIsLoading(true);
    addResult(`📝 手动注册测试: ${testEmail}`);
    
    try {
      const success = await register(testEmail, testPassword, testUsername, 'Manual Test User');
      if (success) {
        addResult('✅ 手动注册成功！');
      } else {
        addResult(`❌ 手动注册失败: ${error}`);
      }
    } catch (err) {
      addResult(`💥 手动注册异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 手动登录测试
  const testManualLogin = async () => {
    if (!testEmail || !testPassword) {
      addResult('❌ 请填写邮箱和密码');
      return;
    }
    
    setIsLoading(true);
    addResult(`🔐 手动登录测试: ${testEmail}`);
    
    try {
      const success = await login(testEmail, testPassword);
      if (success) {
        addResult('✅ 手动登录成功！');
      } else {
        addResult(`❌ 手动登录失败: ${error}`);
      }
    } catch (err) {
      addResult(`💥 手动登录异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 清除本地存储
  const clearLocalStorage = () => {
    localStorage.removeItem('mockauth_users');
    localStorage.removeItem('mock_auth_session');
    addResult('🗑️ 已清除本地存储的用户数据');
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8">注册登录问题修复测试</h1>
          
          {/* 当前状态 */}
          <div className="mb-8 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800 mb-4">当前状态</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>认证状态:</strong> {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}</div>
              <div><strong>用户邮箱:</strong> {user?.email || '无'}</div>
              <div><strong>用户名:</strong> {user?.user_metadata?.username || '无'}</div>
              <div><strong>错误信息:</strong> {error || '无'}</div>
            </div>
          </div>

          {/* 自动测试区域 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4">自动测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={testFullFlow}
                disabled={isLoading}
                className="bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                完整注册-登录流程测试
              </button>
              
              <button
                onClick={testPresetAccounts}
                disabled={isLoading}
                className="bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                预置账户登录测试
              </button>
              
              <button
                onClick={clearResults}
                disabled={isLoading}
                className="bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 disabled:opacity-50"
              >
                清除测试结果
              </button>
            </div>
          </div>

          {/* 手动测试区域 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4">手动测试</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <input
                type="email"
                placeholder="测试邮箱"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              />
              <input
                type="password"
                placeholder="测试密码"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              />
              <input
                type="text"
                placeholder="用户名"
                value={testUsername}
                onChange={(e) => setTestUsername(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <button
                onClick={generateTestAccount}
                disabled={isLoading}
                className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
              >
                生成测试账户
              </button>
              <button
                onClick={testManualRegistration}
                disabled={isLoading}
                className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                手动注册
              </button>
              <button
                onClick={testManualLogin}
                disabled={isLoading}
                className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                手动登录
              </button>
              <button
                onClick={clearLocalStorage}
                disabled={isLoading}
                className="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                清除存储
              </button>
            </div>
          </div>

          {/* 测试结果 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击上方按钮开始测试...</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="break-words">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 说明信息 */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">修复说明</h3>
            <div className="text-sm text-yellow-700 space-y-2">
              <p><strong>问题:</strong> 注册的账户无法登录，因为模拟认证系统的用户数据只存储在内存中，服务器重启后丢失。</p>
              <p><strong>修复:</strong> 添加了 localStorage 持久化存储，注册的用户现在会保存到浏览器本地存储中。</p>
              <p><strong>测试:</strong> 使用"完整注册-登录流程测试"来验证修复效果。</p>
              <p><strong>注意:</strong> 清除存储会删除所有注册的用户数据，但预置账户会自动重新添加。</p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a href="/test-real-accounts" className="text-blue-600 hover:text-blue-800 underline">
              真实邮箱测试页面
            </a>
            <a href="/auth/supabase-login" className="text-blue-600 hover:text-blue-800 underline">
              登录页面
            </a>
            <a href="/auth/supabase-register" className="text-blue-600 hover:text-blue-800 underline">
              注册页面
            </a>
            <a href="/" className="text-blue-600 hover:text-blue-800 underline">
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
