'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';

export default function NewDashboardPage() {
  const { user, isAuthenticated, isLoading, logout } = useSimpleAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('🏠 Dashboard: Checking auth status');
    console.log('🏠 Dashboard: isLoading:', isLoading, 'isAuthenticated:', isAuthenticated);
    
    if (!isLoading && !isAuthenticated) {
      console.log('🚨 Dashboard: Not authenticated, redirecting to login');
      router.push('/new-auth/login');
    }
  }, [isLoading, isAuthenticated, router]);

  const handleLogout = () => {
    logout();
    router.push('/new-auth/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Newzora</h1>
              <span className="ml-4 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                New System
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">Welcome, {user?.username}!</span>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Success Message */}
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-2xl">🎉</span>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium">Login Successful!</h3>
                <p className="mt-1">You have successfully logged in with the new authentication system.</p>
              </div>
            </div>
          </div>

          {/* User Info Card */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                User Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Username</dt>
                  <dd className="mt-1 text-sm text-gray-900">{user?.username}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900">{user?.email}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Role</dt>
                  <dd className="mt-1 text-sm text-gray-900">{user?.role}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">User ID</dt>
                  <dd className="mt-1 text-sm text-gray-900">{user?.id}</dd>
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                System Status
              </h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="text-green-500 text-xl mr-3">✅</span>
                  <span>Authentication system working correctly</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 text-xl mr-3">✅</span>
                  <span>User session active</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 text-xl mr-3">✅</span>
                  <span>Protected route access granted</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 text-xl mr-3">✅</span>
                  <span>Login/logout functionality operational</span>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Links
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a 
                  href="/" 
                  className="bg-blue-500 text-white px-4 py-2 rounded text-center hover:bg-blue-600"
                >
                  🏠 Original Home
                </a>
                <a 
                  href="/new-auth/login" 
                  className="bg-gray-500 text-white px-4 py-2 rounded text-center hover:bg-gray-600"
                >
                  🔐 New Login
                </a>
                <a
                  href="/login"
                  className="bg-purple-500 text-white px-4 py-2 rounded text-center hover:bg-purple-600"
                >
                  🔒 Login
                </a>
                <button
                  onClick={handleLogout}
                  className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                >
                  🚪 Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
