const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const passport = require('./config/passport');
const { sequelize, testConnection } = require('./config/database');
const { apiLimiter } = require('./middleware/rateLimiter');
const { applySecurityMiddleware, rateLimiters } = require('./middleware/security');
const { testEmailConfig } = require('./services/emailService');
const path = require('path');
const http = require('http');
// Load model associations
require('./models/associations');
require('dotenv').config();

// Import logging and monitoring
const { logger } = require('./config/logger');
const {
  requestLogger,
  errorLogger,
  securityLogger,
  performanceMonitor,
  setupDatabaseLogging,
  startHealthMonitoring,
  trackApiUsage
} = require('./middleware/logging');

// Import socket service
const socketService = require('./services/socketService');

const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Apply security middleware (CSP, rate limiting, input validation)
applySecurityMiddleware(app);

// Logging middleware (before other middleware)
app.use(requestLogger);
app.use(securityLogger);
app.use(performanceMonitor);
app.use(trackApiUsage);

// Rate limiting
app.use('/api/', apiLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Cookie parsing middleware
app.use(cookieParser());

// Static file serving for uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Session configuration for Passport
app.use(session({
  secret: process.env.SESSION_SECRET || process.env.JWT_SECRET || 'fallback-secret-key',
  resave: false,
  saveUninitialized: false,
  name: 'onenews.sid', // 自定义会话名称
  cookie: {
    secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS
    httpOnly: true, // 防止XSS攻击
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax' // CSRF保护
  },
  rolling: true // 每次请求都重新设置过期时间
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Authentication routes
app.use('/api/auth', require('./routes/auth'));

// Routes with specific rate limiting
app.use('/api/articles', require('./routes/articles'));
app.use('/api/categories', require('./routes/categories'));

// User routes with enhanced security
app.use('/api/users/login', rateLimiters.auth);
app.use('/api/users/register', rateLimiters.register);
app.use('/api/users/reset-password', rateLimiters.passwordReset);
app.use('/api/users', rateLimiters.api, require('./routes/users'));
app.use('/api/comments', require('./routes/comments'));

// Social features routes
app.use('/api/follows', require('./routes/follows'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/tags', require('./routes/tags'));
app.use('/api/activities', require('./routes/activities'));
app.use('/api/shares', require('./routes/shares'));

// Content management routes
app.use('/api/drafts', require('./routes/drafts'));
app.use('/api/media', require('./routes/media'));
app.use('/api/reviews', require('./routes/reviews'));
app.use('/api/review-rules', require('./routes/review-rules'));

// Analytics routes
app.use('/api/analytics', require('./routes/analytics'));

// Notification routes
app.use('/api/notifications', require('./routes/notifications'));

// Monitoring routes
app.use('/api/monitoring', require('./routes/monitoring'));

// PostgreSQL connection and database sync
const initializeDatabase = async () => {
  try {
    await testConnection();
    await sequelize.sync({ force: false }); // Don't alter existing tables
    logger.info('Database synchronized successfully');

    // Setup database logging
    setupDatabaseLogging(sequelize);
    logger.info('Database logging configured');

    // Test email configuration
    const emailConfigValid = await testEmailConfig();
    if (emailConfigValid) {
      logger.info('Email service configured successfully');
    } else {
      logger.warn('Email service configuration failed - email features may not work');
    }

    // Start health monitoring
    startHealthMonitoring();
    logger.info('Health monitoring started');

  } catch (error) {
    logger.error('Database initialization failed', { error: error.message, stack: error.stack });
    process.exit(1);
  }
};

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Content Hub API is running!' });
});

// Health check endpoint for Docker
app.get('/api/health', async (req, res) => {
  try {
    // Check database connection
    await sequelize.authenticate();

    // Check if all required environment variables are set
    const requiredEnvVars = ['JWT_SECRET', 'DB_HOST', 'DB_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      database: 'connected',
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    };

    if (missingVars.length > 0) {
      healthStatus.warnings = `Missing environment variables: ${missingVars.join(', ')}`;
    }

    res.status(200).json(healthStatus);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      database: 'disconnected'
    });
  }
});

// Error handling middleware (must be after all routes)
app.use(errorLogger);
app.use((err, req, res, next) => {
  // Error already logged by errorLogger middleware
  const isDevelopment = process.env.NODE_ENV !== 'production';

  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(isDevelopment && { stack: err.stack })
  });
});

// Start server
const startServer = async () => {
  try {
    await initializeDatabase();

    // Initialize Socket.IO
    const io = socketService.initialize(server);
    logger.info('Socket.IO initialized');

    // Make socket service available to routes
    app.set('socketService', socketService);

    server.listen(PORT, (err) => {
      if (err) {
        logger.error('Failed to start server', { error: err.message, stack: err.stack });
        process.exit(1);
      }

      logger.info(`Server started successfully`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        apiUrl: `http://localhost:${PORT}/api`,
        socketUrl: `ws://localhost:${PORT}`,
        pid: process.pid
      });

      console.log(`🚀 OneNews Server is running on port ${PORT}`);
      console.log(`📊 API available at http://localhost:${PORT}/api`);
      console.log(`🔌 Socket.IO available at ws://localhost:${PORT}`);
      console.log(`📈 Monitoring available at http://localhost:${PORT}/api/monitoring/health`);
    });

    // Handle server errors
    server.on('error', (err) => {
      logger.error('Server error', { error: err.message, stack: err.stack });
      if (err.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use`);
      }
      process.exit(1);
    });

    // Graceful shutdown handling
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

  } catch (error) {
    logger.error('Server startup failed', { error: error.message, stack: error.stack });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown`);

  server.close(() => {
    logger.info('HTTP server closed');

    sequelize.close().then(() => {
      logger.info('Database connections closed');
      process.exit(0);
    }).catch((error) => {
      logger.error('Error closing database connections', { error: error.message });
      process.exit(1);
    });
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

startServer();
