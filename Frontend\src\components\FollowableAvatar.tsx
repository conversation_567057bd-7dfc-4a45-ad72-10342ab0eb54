'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';

interface User {
  id: number;
  username: string;
  avatar?: string;
  name?: string;
  bio?: string;
  followersCount?: number;
  followingCount?: number;
  worksCount?: number;
  isFollowing?: boolean;
}

interface FollowableAvatarProps {
  user: User;
  size?: 'sm' | 'md' | 'lg';
  showFollowButton?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  className?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function FollowableAvatar({
  user,
  size = 'md',
  showFollowButton = true,
  onFollowChange,
  className = ''
}: FollowableAvatarProps) {
  const { isAuthenticated, user: currentUser, token } = useAuth();
  const toast = useToast();
  const [isFollowing, setIsFollowing] = useState(user.isFollowing || false);
  const [isLoading, setIsLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [followersCount, setFollowersCount] = useState(user.followersCount || 0);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  useEffect(() => {
    setIsFollowing(user.isFollowing || false);
    setFollowersCount(user.followersCount || 0);
  }, [user.isFollowing, user.followersCount]);

  const handleFollow = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error('Please login to follow users');
      return;
    }

    if (!currentUser || currentUser.id === user.id) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/users/${user.id}/follow`, {
        method: isFollowing ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const newFollowingState = !isFollowing;
        
        setIsFollowing(newFollowingState);
        setFollowersCount(data.followersCount || (newFollowingState ? followersCount + 1 : followersCount - 1));
        
        if (onFollowChange) {
          onFollowChange(newFollowingState);
        }

        toast.success(newFollowingState ? `Now following ${user.username}` : `Unfollowed ${user.username}`);
      } else {
        throw new Error('Failed to update follow status');
      }
    } catch (error) {
      console.error('Error updating follow status:', error);
      toast.error('Failed to update follow status');
    } finally {
      setIsLoading(false);
    }
  };

  const avatarContent = (
    <div className={`${sizeClasses[size]} rounded-full overflow-hidden ${className}`}>
      {user.avatar && !imageError ? (
        <img
          src={user.avatar}
          alt={user.username}
          className="w-full h-full object-cover"
          onError={() => setImageError(true)}
        />
      ) : (
        <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
          <span className="text-white font-medium text-lg">
            {user.username.charAt(0).toUpperCase()}
          </span>
        </div>
      )}
    </div>
  );

  return (
    <div className="flex items-center space-x-3">
      {isAuthenticated ? (
        <Link href={`/profile/${user.id}`} className="hover:opacity-80 transition-opacity">
          {avatarContent}
        </Link>
      ) : (
        <div className="cursor-pointer hover:opacity-80 transition-opacity">
          {avatarContent}
        </div>
      )}

      <div className="flex-1 min-w-0">
        {isAuthenticated ? (
          <Link href={`/profile/${user.id}`} className="block">
            <h4 className="font-medium text-gray-900 hover:text-blue-600 transition-colors truncate">
              {user.name || user.username}
            </h4>
            <p className="text-sm text-gray-500 truncate">@{user.username}</p>
          </Link>
        ) : (
          <div>
            <h4 className="font-medium text-gray-900 truncate">
              {user.name || user.username}
            </h4>
            <p className="text-sm text-gray-500 truncate">@{user.username}</p>
          </div>
        )}
        
        <div className="flex items-center space-x-2 text-xs text-gray-400 mt-1">
          <span>{followersCount} followers</span>
          <span>•</span>
          <span>{user.worksCount || 0} works</span>
        </div>
      </div>

      {showFollowButton && isAuthenticated && currentUser && currentUser.id !== user.id && (
        <button
          onClick={handleFollow}
          disabled={isLoading}
          className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ${
            isFollowing
              ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              : 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center space-x-1">
              <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>{isFollowing ? 'Unfollowing...' : 'Following...'}</span>
            </div>
          ) : (
            isFollowing ? 'Following' : 'Follow'
          )}
        </button>
      )}

      {showFollowButton && !isAuthenticated && (
        <button
          onClick={() => toast.info('Please login to follow users')}
          className="px-4 py-2 text-sm font-medium bg-gray-100 text-gray-500 rounded-lg cursor-not-allowed"
        >
          Follow
        </button>
      )}
    </div>
  );
}
