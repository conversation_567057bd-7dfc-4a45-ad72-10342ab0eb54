const { User } = require('../models');

async function activateUsers() {
  try {
    console.log('🔧 Activating all users...');

    // 激活所有用户
    const [updatedCount] = await User.update(
      { 
        isActive: true,
        isEmailVerified: true 
      },
      { 
        where: {} // 更新所有用户
      }
    );

    console.log(`✅ Activated ${updatedCount} users`);

    // 显示所有用户
    const users = await User.findAll({
      attributes: ['id', 'username', 'email', 'role', 'isActive', 'isEmailVerified']
    });

    console.log('\n📋 Current users:');
    users.forEach(user => {
      console.log(`- ${user.username} (${user.email}) - Role: ${user.role} - Active: ${user.isActive} - Verified: ${user.isEmailVerified}`);
    });

  } catch (error) {
    console.error('❌ Error activating users:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  activateUsers().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('<PERSON><PERSON><PERSON> failed:', error);
    process.exit(1);
  });
}

module.exports = activateUsers;
