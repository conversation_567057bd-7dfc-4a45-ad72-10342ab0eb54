'use client';

import React, { forwardRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'gradient';
  hover?: 'none' | 'lift' | 'glow' | 'scale' | 'tilt';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  children: ReactNode;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    variant = 'default',
    hover = 'lift',
    padding = 'md',
    radius = 'xl',
    children,
    ...props
  }, ref) => {
    
    // 基础样式
    const baseStyles = [
      'relative overflow-hidden transition-all duration-300',
      'border border-border'
    ];

    // 变体样式
    const variantStyles = {
      default: [
        'bg-surface',
        'shadow-md'
      ],
      elevated: [
        'bg-surface',
        'shadow-lg shadow-neutral-200/50',
        '[data-theme="dark"] &:shadow-neutral-900/50'
      ],
      outlined: [
        'bg-surface',
        'border-2 border-border',
        'shadow-sm'
      ],
      glass: [
        'bg-white/10 backdrop-blur-md',
        'border border-white/20',
        'shadow-lg shadow-black/5'
      ],
      gradient: [
        'bg-gradient-to-br from-surface to-surface-2',
        'shadow-lg'
      ]
    };

    // 悬停效果样式
    const hoverStyles = {
      none: '',
      lift: [
        'hover:-translate-y-2',
        'hover:shadow-xl hover:shadow-neutral-200/20',
        '[data-theme="dark"] &:hover:shadow-neutral-900/20'
      ],
      glow: [
        'hover:shadow-2xl hover:shadow-primary/20',
        'hover:border-primary/30'
      ],
      scale: [
        'hover:scale-[1.02]',
        'hover:shadow-lg'
      ],
      tilt: [
        'hover:rotate-1',
        'hover:scale-105',
        'hover:shadow-xl'
      ]
    };

    // 内边距样式
    const paddingStyles = {
      none: 'p-0',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8'
    };

    // 圆角样式
    const radiusStyles = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      '2xl': 'rounded-2xl'
    };

    const cardClasses = cn(
      baseStyles,
      variantStyles[variant],
      hoverStyles[hover],
      paddingStyles[padding],
      radiusStyles[radius],
      className
    );

    return (
      <div
        ref={ref}
        className={cardClasses}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card子组件
const CardHeader = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col space-y-1.5', className)}
      {...props}
    />
  )
);
CardHeader.displayName = 'CardHeader';

const CardTitle = forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        'text-xl font-semibold leading-none tracking-tight',
        'text-text-primary',
        className
      )}
      {...props}
    />
  )
);
CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-text-muted', className)}
      {...props}
    />
  )
);
CardDescription.displayName = 'CardDescription';

const CardContent = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('pt-0', className)}
      {...props}
    />
  )
);
CardContent.displayName = 'CardContent';

const CardFooter = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex items-center pt-4', className)}
      {...props}
    />
  )
);
CardFooter.displayName = 'CardFooter';

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
};
