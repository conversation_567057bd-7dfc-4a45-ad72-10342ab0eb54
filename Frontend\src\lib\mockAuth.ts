// 模拟认证系统 - 用于开发和测试
interface MockUser {
  id: string;
  email: string;
  created_at: string;
  user_metadata: {
    username?: string;
    display_name?: string;
  };
}

interface MockSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: MockUser;
}

class MockAuthService {
  private users: Map<string, { password: string; user: MockUser }> = new Map();
  private currentSession: MockSession | null = null;
  private listeners: ((session: MockSession | null) => void)[] = [];

  constructor() {
    // 从 localStorage 恢复用户数据
    this.restoreUsers();

    // 添加测试用户（如果不存在）
    if (!this.users.has('<EMAIL>')) {
      this.addTestUser('<EMAIL>', 'TestPassword123!', 'testuser', 'Test User');
    }
    if (!this.users.has('<EMAIL>')) {
      this.addTestUser('<EMAIL>', 'TestPassword123!', 'testuser2', 'Test User 2');
    }

    // 从 localStorage 恢复会话
    this.restoreSession();
  }

  private addTestUser(email: string, password: string, username: string, displayName: string) {
    const user: MockUser = {
      id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      email,
      created_at: new Date().toISOString(),
      user_metadata: {
        username,
        display_name: displayName
      }
    };

    this.users.set(email, { password, user });
    this.saveUsers(); // 保存用户数据
  }

  // 保存用户数据到 localStorage
  private saveUsers() {
    try {
      const usersData = Array.from(this.users.entries());
      localStorage.setItem('mockauth_users', JSON.stringify(usersData));
    } catch (error) {
      console.warn('Failed to save users to localStorage:', error);
    }
  }

  // 从 localStorage 恢复用户数据
  private restoreUsers() {
    try {
      const usersData = localStorage.getItem('mockauth_users');
      if (usersData) {
        const parsedData = JSON.parse(usersData);
        this.users = new Map(parsedData);
        console.log('✅ MockAuth: 恢复了', this.users.size, '个用户');
      }
    } catch (error) {
      console.warn('Failed to restore users from localStorage:', error);
      this.users = new Map();
    }
  }

  private restoreSession() {
    try {
      const sessionData = localStorage.getItem('mock_auth_session');
      if (sessionData) {
        const session = JSON.parse(sessionData);
        if (session.expires_at > Date.now()) {
          this.currentSession = session;
        } else {
          localStorage.removeItem('mock_auth_session');
        }
      }
    } catch (error) {
      console.error('Failed to restore session:', error);
    }
  }

  private saveSession(session: MockSession | null) {
    if (session) {
      localStorage.setItem('mock_auth_session', JSON.stringify(session));
    } else {
      localStorage.removeItem('mock_auth_session');
    }
  }

  private createSession(user: MockUser): MockSession {
    const session: MockSession = {
      access_token: `mock_token_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      refresh_token: `mock_refresh_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      expires_at: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days
      user
    };

    this.currentSession = session;
    this.saveSession(session);
    this.notifyListeners(session);
    return session;
  }

  private notifyListeners(session: MockSession | null) {
    this.listeners.forEach(listener => listener(session));
  }

  // 注册
  async signUp(email: string, password: string, username: string, displayName?: string): Promise<{ data: { user: MockUser } | null; error: Error | null }> {
    console.log('🔐 MockAuth: 开始注册', { email, username, displayName });
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟

    if (this.users.has(email)) {
      console.log('❌ MockAuth: 用户已存在', email);
      return {
        data: null,
        error: new Error('User already registered')
      };
    }

    const user: MockUser = {
      id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      email,
      created_at: new Date().toISOString(),
      user_metadata: {
        username,
        display_name: displayName || username
      }
    };

    this.users.set(email, { password, user });
    this.saveUsers(); // 保存新注册的用户
    console.log('✅ MockAuth: 注册成功', user.email);

    return {
      data: { user },
      error: null
    };
  }

  // 登录
  async signIn(email: string, password: string): Promise<{ data: { user: MockUser; session: MockSession } | null; error: Error | null }> {
    console.log('🔐 MockAuth: 开始登录', email);
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟

    const userData = this.users.get(email);
    if (!userData || userData.password !== password) {
      console.log('❌ MockAuth: 登录失败 - 无效凭据', email);
      return {
        data: null,
        error: new Error('Invalid login credentials')
      };
    }

    const session = this.createSession(userData.user);
    console.log('✅ MockAuth: 登录成功', userData.user.email);

    return {
      data: { user: userData.user, session },
      error: null
    };
  }

  // 登出
  async signOut(): Promise<{ error: Error | null }> {
    await new Promise(resolve => setTimeout(resolve, 200)); // 模拟网络延迟

    this.currentSession = null;
    this.saveSession(null);
    this.notifyListeners(null);

    return { error: null };
  }

  // 获取当前会话
  async getSession(): Promise<{ data: { session: MockSession | null }; error: Error | null }> {
    return {
      data: { session: this.currentSession },
      error: null
    };
  }

  // 重置密码
  async resetPasswordForEmail(email: string): Promise<{ data: {} | null; error: Error | null }> {
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟

    if (!this.users.has(email)) {
      return {
        data: null,
        error: new Error('User not found')
      };
    }

    // 在真实环境中，这里会发送邮件
    console.log(`Mock: Password reset email sent to ${email}`);

    return {
      data: {},
      error: null
    };
  }

  // 更新密码
  async updateUser(attributes: { password: string }): Promise<{ data: { user: MockUser } | null; error: Error | null }> {
    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟网络延迟

    if (!this.currentSession) {
      return {
        data: null,
        error: new Error('Not authenticated')
      };
    }

    // 更新密码
    const userData = this.users.get(this.currentSession.user.email);
    if (userData) {
      userData.password = attributes.password;
    }

    return {
      data: { user: this.currentSession.user },
      error: null
    };
  }

  // 监听认证状态变化
  onAuthStateChange(callback: (event: string, session: MockSession | null) => void) {
    const listener = (session: MockSession | null) => {
      const event = session ? 'SIGNED_IN' : 'SIGNED_OUT';
      callback(event, session);
    };

    this.listeners.push(listener);

    // 立即调用一次以获取当前状态
    listener(this.currentSession);

    // 返回取消订阅函数
    return {
      data: {
        subscription: {
          unsubscribe: () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
              this.listeners.splice(index, 1);
            }
          }
        }
      }
    };
  }

  // 社交登录（模拟）
  async signInWithOAuth(provider: { provider: string }) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟重定向延迟

    // 在真实环境中，这会重定向到社交登录提供商
    console.log(`Mock: Redirecting to ${provider.provider} OAuth`);
    
    // 模拟成功登录
    const mockUser: MockUser = {
      id: `${provider.provider}_user_${Date.now()}`,
      email: `user@${provider.provider}.com`,
      created_at: new Date().toISOString(),
      user_metadata: {
        username: `${provider.provider}user`,
        display_name: `${provider.provider} User`
      }
    };

    const session = this.createSession(mockUser);

    return {
      data: { url: null },
      error: null
    };
  }
}

// 创建单例实例
export const mockAuth = new MockAuthService();

// 导出与 Supabase 兼容的接口
export const mockSupabase = {
  auth: {
    signUp: mockAuth.signUp.bind(mockAuth),
    signInWithPassword: mockAuth.signIn.bind(mockAuth),
    signOut: mockAuth.signOut.bind(mockAuth),
    getSession: mockAuth.getSession.bind(mockAuth),
    resetPasswordForEmail: mockAuth.resetPasswordForEmail.bind(mockAuth),
    updateUser: mockAuth.updateUser.bind(mockAuth),
    onAuthStateChange: mockAuth.onAuthStateChange.bind(mockAuth),
    signInWithOAuth: mockAuth.signInWithOAuth.bind(mockAuth)
  }
};
