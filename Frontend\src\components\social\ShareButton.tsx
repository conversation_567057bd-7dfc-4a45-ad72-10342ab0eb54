'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useToast } from '@/components/Toast';
import { 
  ShareIcon, 
  LinkIcon, 
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface ShareButtonProps {
  articleId: number;
  title: string;
  url?: string;
  description?: string;
  image?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button' | 'text';
  className?: string;
}

interface SharePlatform {
  name: string;
  label: string;
  icon: string;
  color: string;
  getUrl: (url: string, title: string, description?: string) => string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

const sharePlatforms: SharePlatform[] = [
  {
    name: 'facebook',
    label: 'Facebook',
    icon: '📘',
    color: 'bg-blue-600 hover:bg-blue-700',
    getUrl: (url, title) =>
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(title)}`
  },
  {
    name: 'twitter',
    label: 'Twitter',
    icon: '🐦',
    color: 'bg-sky-500 hover:bg-sky-600',
    getUrl: (url, title) =>
      `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`
  },
  {
    name: 'linkedin',
    label: 'LinkedIn',
    icon: '💼',
    color: 'bg-blue-700 hover:bg-blue-800',
    getUrl: (url) =>
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
  },
  {
    name: 'whatsapp',
    label: 'WhatsApp',
    icon: '💬',
    color: 'bg-green-500 hover:bg-green-600',
    getUrl: (url, title) =>
      `https://wa.me/?text=${encodeURIComponent(`${title} ${url}`)}`
  },
  {
    name: 'telegram',
    label: 'Telegram',
    icon: '✈️',
    color: 'bg-blue-500 hover:bg-blue-600',
    getUrl: (url, title) =>
      `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`
  },
  {
    name: 'email',
    label: 'Email',
    icon: '📧',
    color: 'bg-gray-600 hover:bg-gray-700',
    getUrl: (url, title, description) => 
      `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(`${description || title}\n\n${url}`)}`
  }
];

export default function ShareButton({
  articleId,
  title,
  url,
  description,
  size = 'md',
  variant = 'button',
  className = ''
}: ShareButtonProps) {
  const toast = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '');

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const recordShare = async (platform: string) => {
    try {
      await fetch(`${API_BASE_URL}/shares`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId,
          platform,
          referrer: typeof window !== 'undefined' ? window.location.href : ''
        }),
      });
    } catch (error) {
      console.error('Error recording share:', error);
    }
  };

  const handlePlatformShare = async (platform: SharePlatform) => {
    const shareLink = platform.getUrl(shareUrl, title, description);
    
    // Record the share
    await recordShare(platform.name);
    
    // Open share window
    if (platform.name === 'email') {
      window.location.href = shareLink;
    } else {
      window.open(
        shareLink,
        'share-window',
        'width=600,height=400,scrollbars=yes,resizable=yes'
      );
    }
    
    setIsOpen(false);
    toast.success('Share Successful', `Shared via ${platform.label}`);
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      await recordShare('copy_link');
      toast.success('Link Copied', 'Share link copied to clipboard');
      
      setTimeout(() => {
        setCopied(false);
        setIsOpen(false);
      }, 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Copy Failed', 'Unable to copy link to clipboard');
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url: shareUrl,
        });
        await recordShare('native');
        toast.success('Share Successful', 'Shared via system');
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
          toast.error('Share Failed', 'System share function unavailable');
        }
      }
    } else {
      setIsOpen(!isOpen);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3'
  };

  const iconSizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  // Render different variants
  const renderTrigger = () => {
    const iconClass = `${iconSizeClasses[size]} text-gray-600`;
    
    switch (variant) {
      case 'icon':
        return (
          <button
            onClick={handleNativeShare}
            className={`
              ${sizeClasses[size]} rounded-full hover:bg-gray-100 transition-colors
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              ${className}
            `}
            title="Share"
          >
            <ShareIcon className={iconClass} />
          </button>
        );
      
      case 'text':
        return (
          <button
            onClick={handleNativeShare}
            className={`
              inline-flex items-center text-sm text-gray-600 hover:text-blue-600 transition-colors
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              ${className}
            `}
          >
            <ShareIcon className="h-4 w-4 mr-1" />
            Share
          </button>
        );
      
      default: // button
        return (
          <button
            onClick={handleNativeShare}
            className={`
              inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm
              text-sm font-medium text-gray-700 bg-white hover:bg-gray-50
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
              transition-colors
              ${className}
            `}
          >
            <ShareIcon className="h-4 w-4 mr-2" />
            Share
          </button>
        );
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {renderTrigger()}
      
      {/* Share Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
          <div className="px-4 py-2 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-900">Share to</h3>
          </div>
          
          {/* Copy Link */}
          <button
            onClick={handleCopyLink}
            className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center transition-colors"
          >
            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 mr-3">
              {copied ? (
                <CheckIcon className="h-4 w-4 text-green-600" />
              ) : (
                <LinkIcon className="h-4 w-4 text-gray-600" />
              )}
            </div>
            <span className="text-sm text-gray-700">
              {copied ? 'Link Copied' : 'Copy Link'}
            </span>
          </button>
          
          {/* Platform Buttons */}
          {sharePlatforms.map((platform) => (
            <button
              key={platform.name}
              onClick={() => handlePlatformShare(platform)}
              className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center transition-colors"
            >
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 mr-3">
                <span className="text-sm">{platform.icon}</span>
              </div>
              <span className="text-sm text-gray-700">{platform.label}</span>
            </button>
          ))}
          
          {/* Close Button */}
          <div className="border-t border-gray-100 mt-2 pt-2">
            <button
              onClick={() => setIsOpen(false)}
              className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-center transition-colors"
            >
              <XMarkIcon className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-500">Close</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
