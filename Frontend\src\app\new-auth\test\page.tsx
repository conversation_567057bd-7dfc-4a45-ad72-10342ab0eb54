'use client';

import { useSimpleAuth } from '@/contexts/SimpleAuthContext';

export default function NewAuthTestPage() {
  const { user, token, isAuthenticated, isLoading, login, logout } = useSimpleAuth();

  const handleQuickLogin = async () => {
    const result = await login('<EMAIL>', 'Qw12345');
    console.log('Quick login result:', result);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🧪 New Authentication System Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Current Status */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">📊 Current Status</h2>
            <div className="space-y-2">
              <p><strong>Loading:</strong> <span className={isLoading ? 'text-yellow-600' : 'text-green-600'}>{String(isLoading)}</span></p>
              <p><strong>Authenticated:</strong> <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>{String(isAuthenticated)}</span></p>
              <p><strong>User:</strong> {user ? `${user.username} (${user.email})` : 'None'}</p>
              <p><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}</p>
            </div>
          </div>

          {/* Storage Status */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">💾 Storage Status</h2>
            <div className="space-y-2">
              <p><strong>localStorage token:</strong> {typeof window !== 'undefined' && localStorage.getItem('auth_token') ? '✅ Present' : '❌ Missing'}</p>
              <p><strong>localStorage user:</strong> {typeof window !== 'undefined' && localStorage.getItem('auth_user') ? '✅ Present' : '❌ Missing'}</p>
              <p><strong>Cookies:</strong> {typeof window !== 'undefined' && document.cookie.includes('auth_token') ? '✅ Present' : '❌ Missing'}</p>
            </div>
          </div>

          {/* Actions */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">🎮 Actions</h2>
            <div className="space-y-3">
              <button 
                onClick={handleQuickLogin}
                className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                🔐 Quick Login Test
              </button>
              
              <button 
                onClick={logout}
                className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                🚪 Logout
              </button>
              
              <a 
                href="/new-auth/login"
                className="block w-full bg-green-500 text-white px-4 py-2 rounded text-center hover:bg-green-600"
              >
                🔑 Go to Login Page
              </a>
              
              <a 
                href="/new-auth/dashboard"
                className="block w-full bg-purple-500 text-white px-4 py-2 rounded text-center hover:bg-purple-600"
              >
                🏠 Go to Dashboard
              </a>
            </div>
          </div>

          {/* System Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">ℹ️ System Info</h2>
            <div className="space-y-2 text-sm">
              <p><strong>System:</strong> New Simple Auth</p>
              <p><strong>Provider:</strong> SimpleAuthProvider</p>
              <p><strong>Hook:</strong> useSimpleAuth</p>
              <p><strong>Storage:</strong> localStorage + cookies</p>
              <p><strong>API:</strong> Direct fetch calls</p>
            </div>
          </div>
        </div>

        {/* Comparison */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">🔄 System Comparison</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-red-600 mb-2">❌ Old System Issues</h3>
              <ul className="text-sm space-y-1">
                <li>• Complex AuthContext with multiple states</li>
                <li>• useEffect dependency loops</li>
                <li>• Cookie SameSite conflicts</li>
                <li>• State synchronization issues</li>
                <li>• Middleware timing problems</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold text-green-600 mb-2">✅ New System Benefits</h3>
              <ul className="text-sm space-y-1">
                <li>• Simple, focused AuthContext</li>
                <li>• No complex useEffect chains</li>
                <li>• Consistent cookie settings</li>
                <li>• Direct state management</li>
                <li>• Predictable behavior</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">🧭 Navigation</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="/login" className="text-blue-600 hover:underline">🔒 Main Login</a>
            <a href="/new-auth/login" className="text-blue-600 hover:underline">🔑 New Login</a>
            <a href="/" className="text-blue-600 hover:underline">🏠 Original Home</a>
            <a href="/new-auth/dashboard" className="text-blue-600 hover:underline">📊 New Dashboard</a>
          </div>
        </div>
      </div>
    </div>
  );
}
