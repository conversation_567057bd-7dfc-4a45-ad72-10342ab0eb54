/**
 * 安全配置模块
 * 包含JWT、密码、会话等安全相关的配置和工具函数
 */

const crypto = require('crypto');
const bcrypt = require('bcryptjs');

// 安全配置常量
const SECURITY_CONFIG = {
  // JWT配置
  JWT: {
    ALGORITHM: 'HS256',
    ISSUER: 'Newzora',
    AUDIENCE: 'Newzora-Users',
    // 生产环境应该更短，如1-2小时，配合refresh token
    ACCESS_TOKEN_EXPIRES: process.env.NODE_ENV === 'production' ? '2h' : '7d',
    REFRESH_TOKEN_EXPIRES: '30d'
  },
  
  // 密码配置
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    // 密码复杂度要求
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
    // 密码历史记录数量
    PASSWORD_HISTORY_COUNT: 5
  },
  
  // 账户锁定配置 - 测试期间禁用
  ACCOUNT_LOCK: {
    MAX_LOGIN_ATTEMPTS: 999999, // 测试期间不限制登录尝试次数
    LOCK_TIME: 30 * 60 * 1000, // 30分钟
    PROGRESSIVE_DELAY: false // 测试期间禁用渐进式延迟
  },
  
  // 会话配置
  SESSION: {
    SECURE: process.env.NODE_ENV === 'production',
    HTTP_ONLY: true,
    SAME_SITE: 'strict',
    MAX_AGE: 24 * 60 * 60 * 1000, // 24小时
    REGENERATE_ON_LOGIN: true
  },
  
  // 令牌配置
  TOKENS: {
    EMAIL_VERIFICATION_EXPIRES: 24 * 60 * 60 * 1000, // 24小时
    PASSWORD_RESET_EXPIRES: 60 * 60 * 1000, // 1小时
    REFRESH_TOKEN_LENGTH: 64,
    CSRF_TOKEN_LENGTH: 32
  }
};

/**
 * 生成强随机密钥
 * @param {number} length - 密钥长度（字节）
 * @returns {string} Base64编码的密钥
 */
function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('base64');
}

/**
 * 生成安全的随机令牌
 * @param {number} length - 令牌长度（字节）
 * @returns {string} 十六进制编码的令牌
 */
function generateSecureToken(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 验证密码强度
 * @param {string} password - 待验证的密码
 * @returns {Object} 验证结果
 */
function validatePasswordStrength(password) {
  const result = {
    isValid: true,
    errors: [],
    score: 0
  };

  // 长度检查
  if (password.length < SECURITY_CONFIG.PASSWORD.MIN_LENGTH) {
    result.errors.push(`Password must be at least ${SECURITY_CONFIG.PASSWORD.MIN_LENGTH} characters`);
    result.isValid = false;
  } else if (password.length >= SECURITY_CONFIG.PASSWORD.MIN_LENGTH) {
    result.score += 1;
  }

  if (password.length > SECURITY_CONFIG.PASSWORD.MAX_LENGTH) {
    result.errors.push(`Password cannot exceed ${SECURITY_CONFIG.PASSWORD.MAX_LENGTH} characters`);
    result.isValid = false;
  }

  // 复杂度检查
  if (SECURITY_CONFIG.PASSWORD.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    result.errors.push('Password must contain at least one uppercase letter');
    result.isValid = false;
  } else if (/[A-Z]/.test(password)) {
    result.score += 1;
  }

  if (SECURITY_CONFIG.PASSWORD.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    result.errors.push('Password must contain at least one lowercase letter');
    result.isValid = false;
  } else if (/[a-z]/.test(password)) {
    result.score += 1;
  }

  if (SECURITY_CONFIG.PASSWORD.REQUIRE_NUMBERS && !/\d/.test(password)) {
    result.errors.push('Password must contain at least one number');
    result.isValid = false;
  } else if (/\d/.test(password)) {
    result.score += 1;
  }

  if (SECURITY_CONFIG.PASSWORD.REQUIRE_SPECIAL_CHARS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.errors.push('Password must contain at least one special character');
    result.isValid = false;
  } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.score += 1;
  }

  // 常见密码检查
  const commonPasswords = [
    'password', '123456', '123456789', 'qwerty', 'abc123',
    'password123', 'admin', 'letmein', 'welcome', '123123'
  ];

  if (commonPasswords.includes(password.toLowerCase())) {
    result.errors.push('Cannot use common passwords');
    result.isValid = false;
  }

  // 计算密码强度等级
  if (result.score >= 4) {
    result.strength = 'strong';
  } else if (result.score >= 3) {
    result.strength = 'medium';
  } else {
    result.strength = 'weak';
  }

  return result;
}

/**
 * 安全地哈希密码
 * @param {string} password - 原始密码
 * @returns {Promise<string>} 哈希后的密码
 */
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(SECURITY_CONFIG.PASSWORD.BCRYPT_ROUNDS);
  return await bcrypt.hash(password, salt);
}

/**
 * 验证密码
 * @param {string} password - 原始密码
 * @param {string} hash - 哈希值
 * @returns {Promise<boolean>} 验证结果
 */
async function verifyPassword(password, hash) {
  return await bcrypt.compare(password, hash);
}

/**
 * 生成JWT载荷
 * @param {Object} user - 用户对象
 * @returns {Object} JWT载荷
 */
function generateJWTPayload(user) {
  return {
    userId: user.id,
    username: user.username,
    role: user.role,
    iss: SECURITY_CONFIG.JWT.ISSUER,
    aud: SECURITY_CONFIG.JWT.AUDIENCE,
    iat: Math.floor(Date.now() / 1000),
    // 添加用户会话标识，用于强制登出
    sessionId: generateSecureToken(16)
  };
}

/**
 * 计算登录延迟时间（渐进式延迟）
 * @param {number} attempts - 失败尝试次数
 * @returns {number} 延迟时间（毫秒）
 */
function calculateLoginDelay(attempts) {
  if (!SECURITY_CONFIG.ACCOUNT_LOCK.PROGRESSIVE_DELAY) {
    return 0;
  }
  
  // 指数退避算法：2^attempts * 1000ms，最大30秒
  const delay = Math.min(Math.pow(2, attempts) * 1000, 30000);
  return delay;
}

/**
 * 生成CSRF令牌
 * @returns {string} CSRF令牌
 */
function generateCSRFToken() {
  return generateSecureToken(SECURITY_CONFIG.TOKENS.CSRF_TOKEN_LENGTH);
}

/**
 * 安全地比较两个字符串（防止时序攻击）
 * @param {string} a - 字符串A
 * @param {string} b - 字符串B
 * @returns {boolean} 比较结果
 */
function secureCompare(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  
  return result === 0;
}

/**
 * 清理敏感数据（用于日志记录）
 * @param {Object} data - 原始数据
 * @returns {Object} 清理后的数据
 */
function sanitizeForLogging(data) {
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

module.exports = {
  SECURITY_CONFIG,
  generateSecureKey,
  generateSecureToken,
  validatePasswordStrength,
  hashPassword,
  verifyPassword,
  generateJWTPayload,
  calculateLoginDelay,
  generateCSRFToken,
  secureCompare,
  sanitizeForLogging
};
