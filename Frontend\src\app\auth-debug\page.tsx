'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

export default function AuthDebugPage() {
  const { user, token, isAuthenticated, loading, error, login, logout } = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [testCredentials, setTestCredentials] = useState({
    email: '<EMAIL>',
    password: 'Qw12345'
  });
  const router = useRouter();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addLog('AuthDebug 页面加载');
    addLog(`初始认证状态: ${isAuthenticated}`);
    addLog(`初始用户: ${user?.username || 'null'}`);
  }, []);

  useEffect(() => {
    addLog(`认证状态变化: ${isAuthenticated}`);
  }, [isAuthenticated]);

  useEffect(() => {
    addLog(`用户状态变化: ${user?.username || 'null'}`);
  }, [user]);

  const handleTestLogin = async () => {
    addLog('开始测试登录...');
    const success = await login(testCredentials.email, testCredentials.password);
    addLog(`登录结果: ${success ? '成功' : '失败'}`);
    
    if (success) {
      addLog('登录成功，等待跳转...');
      setTimeout(() => {
        addLog('执行跳转到主页');
        router.push('/');
      }, 2000);
    }
  };

  const handleLogout = () => {
    addLog('执行登出...');
    logout();
    addLog('登出完成');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">认证调试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 当前状态 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">当前认证状态</h2>
            <div className="space-y-2">
              <p><strong>用户:</strong> {user?.username || 'null'}</p>
              <p><strong>邮箱:</strong> {user?.email || 'null'}</p>
              <p><strong>Token:</strong> {token ? `${token.substring(0, 30)}...` : 'null'}</p>
              <p><strong>已认证:</strong> {isAuthenticated ? '✅ 是' : '❌ 否'}</p>
              <p><strong>加载中:</strong> {loading ? '⏳ 是' : '✅ 否'}</p>
              <p><strong>错误:</strong> {error || '无'}</p>
            </div>
          </div>

          {/* 测试操作 */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">测试操作</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">邮箱</label>
                <input
                  type="email"
                  value={testCredentials.email}
                  onChange={(e) => setTestCredentials({...testCredentials, email: e.target.value})}
                  className="w-full px-3 py-2 border rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">密码</label>
                <input
                  type="password"
                  value={testCredentials.password}
                  onChange={(e) => setTestCredentials({...testCredentials, password: e.target.value})}
                  className="w-full px-3 py-2 border rounded-md"
                />
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleTestLogin}
                  disabled={loading}
                  className="flex-1 bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? '登录中...' : '测试登录'}
                </button>
                <button
                  onClick={handleLogout}
                  className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                >
                  登出
                </button>
              </div>
            </div>
          </div>

          {/* 调试日志 */}
          <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">调试日志</h2>
              <button
                onClick={clearLogs}
                className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
              >
                清除日志
              </button>
            </div>
            <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500">暂无日志</p>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono">
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
