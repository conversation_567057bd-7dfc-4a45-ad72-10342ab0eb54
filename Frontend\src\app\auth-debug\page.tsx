'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { clearAllAuthData, checkAuthConsistency, forceAuthRefresh, debugAuthState } from '@/utils/authUtils';

export default function AuthDebugPage() {
  const [result, setResult] = useState('');
  
  // 获取两个AuthContext的状态
  const mainAuth = useAuth();
  const supabaseAuth = useSupabaseAuth();

  const testLogin = async () => {
    setResult('正在测试登录...\n');
    
    try {
      console.log('🔍 开始登录测试');
      
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 重要：包含cookies
        body: JSON.stringify({
          identifier: '<EMAIL>',
          password: 'TestPassword123!'
        }),
      });

      const data = await response.json();
      console.log('📡 登录响应:', data);

      if (data.success) {
        setResult(prev => prev + `✅ 登录成功！\n`);
        setResult(prev => prev + `Token: ${data.token.substring(0, 20)}...\n`);
        setResult(prev => prev + `用户: ${data.user.username}\n\n`);
        
        // 保存到localStorage（为了兼容性）
        localStorage.setItem('auth_token', data.token);
        localStorage.setItem('auth_user', JSON.stringify(data.user));
        
        // 检查cookies
        const cookies = document.cookie;
        setResult(prev => prev + `Cookies: ${cookies}\n\n`);
        
        // 等待一下让AuthContext更新
        setTimeout(() => {
          setResult(prev => prev + `主AuthContext状态:\n`);
          setResult(prev => prev + `- isAuthenticated: ${mainAuth.isAuthenticated}\n`);
          setResult(prev => prev + `- user: ${mainAuth.user?.username || '无'}\n`);
          setResult(prev => prev + `- isLoading: ${mainAuth.isLoading}\n\n`);
          
          setResult(prev => prev + `SupabaseAuth状态:\n`);
          setResult(prev => prev + `- isAuthenticated: ${supabaseAuth.isAuthenticated}\n`);
          setResult(prev => prev + `- user: ${supabaseAuth.user?.email || '无'}\n`);
          setResult(prev => prev + `- isLoading: ${supabaseAuth.isLoading}\n`);
        }, 1000);
        
      } else {
        setResult(prev => prev + `❌ 登录失败: ${data.message}\n`);
      }
    } catch (error) {
      console.error('💥 登录错误:', error);
      setResult(prev => prev + `💥 网络错误: ${error}\n`);
    }
  };

  const checkAuthState = () => {
    setResult('检查认证状态...\n\n');
    
    setResult(prev => prev + `主AuthContext状态:\n`);
    setResult(prev => prev + `- isAuthenticated: ${mainAuth.isAuthenticated}\n`);
    setResult(prev => prev + `- user: ${mainAuth.user?.username || '无'}\n`);
    setResult(prev => prev + `- token: ${mainAuth.token ? mainAuth.token.substring(0, 20) + '...' : '无'}\n`);
    setResult(prev => prev + `- isLoading: ${mainAuth.isLoading}\n`);
    setResult(prev => prev + `- error: ${mainAuth.error || '无'}\n\n`);
    
    setResult(prev => prev + `SupabaseAuth状态:\n`);
    setResult(prev => prev + `- isAuthenticated: ${supabaseAuth.isAuthenticated}\n`);
    setResult(prev => prev + `- user: ${supabaseAuth.user?.email || '无'}\n`);
    setResult(prev => prev + `- isLoading: ${supabaseAuth.isLoading}\n`);
    setResult(prev => prev + `- error: ${supabaseAuth.error || '无'}\n\n`);
    
    // 检查localStorage
    const localToken = localStorage.getItem('auth_token');
    const localUser = localStorage.getItem('auth_user');
    setResult(prev => prev + `localStorage:\n`);
    setResult(prev => prev + `- token: ${localToken ? localToken.substring(0, 20) + '...' : '无'}\n`);
    setResult(prev => prev + `- user: ${localUser ? JSON.parse(localUser).username : '无'}\n\n`);
    
    // 检查cookies
    const cookies = document.cookie;
    setResult(prev => prev + `Cookies: ${cookies || '无'}\n`);
  };

  const clearAll = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'user_role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    setResult('已清除所有认证信息\n');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">认证状态调试工具</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <button
              onClick={testLogin}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
            >
              测试登录
            </button>
            
            <button
              onClick={checkAuthState}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
            >
              检查认证状态
            </button>
            
            <button
              onClick={clearAll}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
            >
              清除所有认证信息
            </button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded-md">
            <h3 className="font-bold mb-2">调试结果：</h3>
            <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-96">
              {result || '点击按钮开始测试...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
