# 🚀 Newzora - Modern Content Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-black.svg)](https://nextjs.org/)

A modern, full-featured content platform built with Next.js, Node.js, and PostgreSQL. Newzora provides a complete solution for content creation, user management, and social interaction.

## ✨ Features

### 🔐 Authentication & User Management
- **JWT-based Authentication** - Secure token-based authentication
- **User Registration & Login** - Complete user account management
- **Social Login UI** - Google, Facebook, X, Apple integration (UI ready)
- **Password Reset** - Email-based password recovery
- **Profile Management** - User profiles with avatars and bio

### ✏️ Content Creation & Management
- **Rich Text Editor** - Custom contentEditable-based editor with formatting
- **Article Management** - Create, edit, publish, and manage articles
- **Media Support** - Image insertion and media handling
- **Auto-save** - Automatic draft saving functionality
- **Categories & Tags** - Organize content with categories and tags

### 📱 User Experience
- **Responsive Design** - Mobile-first, fully responsive interface
- **Infinite Scroll** - Smooth content loading on homepage and explore page
- **Real-time Interactions** - Like, comment, share functionality
- **Search & Discovery** - Find content and users easily
- **Notifications** - User notification system

### 🏗️ Technical Features
- **PostgreSQL Database** - Robust data persistence with relationships
- **RESTful API** - Complete backend API with proper error handling
- **Security** - Input validation, rate limiting, CORS protection
- **Performance** - Optimized queries and caching strategies
- **Testing** - Unit and integration tests with Jest

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 13+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Jacken22/OneNews.git
   cd OneNews
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd Backend
   npm install
   
   # Frontend
   cd ../Frontend
   npm install
   ```

3. **Setup environment variables**
   ```bash
   # Backend/.env
   DATABASE_URL=postgresql://postgres:password@localhost:5432/newzora
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRES_IN=7d
   PORT=5000
   ```

4. **Initialize database**
   ```bash
   cd Backend
   npm run init-db
   ```

5. **Start the application**
   ```bash
   # Terminal 1 - Backend
   cd Backend
   npm start
   
   # Terminal 2 - Frontend
   cd Frontend
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 👤 Test Accounts

```
Admin Account:
Email: <EMAIL>
Password: admin123456

Demo Account:
Email: <EMAIL>
Password: demo123456
```

## 📁 Project Structure

```
Newzora/
├── Backend/                 # Node.js/Express API server
│   ├── config/             # Database and app configuration
│   ├── middleware/         # Authentication, security, rate limiting
│   ├── models/             # Sequelize database models
│   ├── routes/             # API route handlers
│   ├── scripts/            # Database initialization and utilities
│   └── server.js           # Main server file
├── Frontend/               # Next.js React application
│   ├── src/
│   │   ├── app/           # Next.js app router pages
│   │   ├── components/    # Reusable React components
│   │   ├── contexts/      # React context providers
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Utility functions
│   └── package.json
├── deployment/            # Docker and deployment configurations
├── docs/                  # Documentation and reports
└── tools/                 # Development tools and scripts
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 14** - React framework with app router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - Modern React state management

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **PostgreSQL** - Relational database
- **Sequelize** - ORM for database operations
- **JWT** - JSON Web Tokens for authentication
- **bcrypt** - Password hashing

### DevOps & Tools
- **Docker** - Containerization
- **Jest** - Testing framework
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     # User registration
POST /api/auth/login        # User login
GET  /api/auth/me          # Get current user
PUT  /api/auth/profile     # Update user profile
PUT  /api/auth/change-password # Change password
```

### Article Endpoints
```
GET    /api/articles       # Get articles list
GET    /api/articles/:id   # Get single article
POST   /api/articles       # Create article (auth required)
PUT    /api/articles/:id   # Update article (auth required)
DELETE /api/articles/:id   # Delete article (auth required)
```

### Comment Endpoints
```
GET    /api/comments/:articleId # Get article comments
POST   /api/comments           # Create comment (auth required)
PUT    /api/comments/:id       # Update comment (auth required)
DELETE /api/comments/:id       # Delete comment (auth required)
```

## 🧪 Testing

```bash
# Backend tests
cd Backend
npm test

# Frontend tests
cd Frontend
npm test

# Run all tests
npm run test:all
```

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production
```bash
# Using Docker
docker-compose -f deployment/docker/docker-compose.prod.yml up

# Manual deployment
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern web technologies
- Inspired by contemporary content platforms
- Community-driven development approach

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.

---

**Made with ❤️ by the Newzora Team**
