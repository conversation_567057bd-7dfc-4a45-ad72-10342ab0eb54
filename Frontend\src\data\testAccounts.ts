// 测试账户数据
export const testAccounts = [
  {
    id: 1,
    username: 'john_writer',
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>',
    role: 'user',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=3b82f6&color=fff',
    bio: 'Tech writer and AI enthusiast. Love exploring new technologies.',
    followers: 1250,
    following: 340,
    articlesCount: 15,
    verified: true,
    joinedAt: '2023-06-15'
  },
  {
    id: 2,
    username: 'sarah_travel',
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>',
    role: 'user',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=8b5cf6&color=fff',
    bio: 'Travel blogger sharing adventures from around the world.',
    followers: 890,
    following: 520,
    articlesCount: 23,
    verified: true,
    joinedAt: '2023-08-22'
  },
  {
    id: 3,
    username: 'mike_finance',
    email: '<EMAIL>',
    password: 'password123',
    name: '<PERSON>',
    role: 'user',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=10b981&color=fff',
    bio: 'Financial analyst and crypto enthusiast. Sharing market insights.',
    followers: 2100,
    following: 180,
    articlesCount: 31,
    verified: true,
    joinedAt: '2023-05-10'
  },
  {
    id: 4,
    username: 'admin_user',
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin User',
    role: 'admin',
    avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=ef4444&color=fff',
    bio: 'Platform administrator and content moderator.',
    followers: 500,
    following: 100,
    articlesCount: 8,
    verified: true,
    joinedAt: '2023-01-01'
  },
  {
    id: 5,
    username: 'lisa_lifestyle',
    email: '<EMAIL>',
    password: 'password123',
    name: 'Lisa Wang',
    role: 'user',
    avatar: 'https://ui-avatars.com/api/?name=Lisa+Wang&background=f59e0b&color=fff',
    bio: 'Lifestyle blogger focusing on wellness and mindfulness.',
    followers: 1580,
    following: 420,
    articlesCount: 19,
    verified: false,
    joinedAt: '2023-09-05'
  }
];

// 登录验证函数
export const validateLogin = (email: string, password: string) => {
  const user = testAccounts.find(account => 
    account.email === email && account.password === password
  );
  
  if (user) {
    // 返回用户信息（不包含密码）
    const { password: _, ...userInfo } = user;
    return {
      success: true,
      user: userInfo,
      token: `mock_token_${user.id}_${Date.now()}`
    };
  }
  
  return {
    success: false,
    message: 'Invalid email or password'
  };
};

// 获取用户信息
export const getUserById = (id: number) => {
  const user = testAccounts.find(account => account.id === id);
  if (user) {
    const { password: _, ...userInfo } = user;
    return userInfo;
  }
  return null;
};

// 检查用户名是否存在
export const checkUsernameExists = (username: string) => {
  return testAccounts.some(account => account.username === username);
};

// 检查邮箱是否存在
export const checkEmailExists = (email: string) => {
  return testAccounts.some(account => account.email === email);
};
