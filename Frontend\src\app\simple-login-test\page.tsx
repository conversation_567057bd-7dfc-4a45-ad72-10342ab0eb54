'use client';

import React, { useState } from 'react';

export default function SimpleLoginTestPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('TestPassword123!');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const testLogin = async () => {
    setIsLoading(true);
    setResult('🔐 开始登录测试...\n');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password: password
        })
      });

      const data = await response.json();
      
      setResult(prev => prev + `📡 API响应状态: ${response.status}\n`);
      setResult(prev => prev + `📊 响应数据: ${JSON.stringify(data, null, 2)}\n`);
      
      if (response.ok && data.success) {
        setResult(prev => prev + '✅ 登录成功！\n');
        setResult(prev => prev + `👤 用户: ${data.user.username} (${data.user.email})\n`);
        setResult(prev => prev + `🔑 Token: ${data.token.substring(0, 30)}...\n`);
        setResult(prev => prev + '🎉 测试完成，没有跳转！\n');
      } else {
        setResult(prev => prev + `❌ 登录失败: ${data.message}\n`);
      }
    } catch (error) {
      setResult(prev => prev + `💥 网络错误: ${error}\n`);
    } finally {
      setIsLoading(false);
    }
  };

  const testHotmail = () => {
    setEmail('<EMAIL>');
    setPassword('TestPassword123!');
  };

  const testGmail = () => {
    setEmail('<EMAIL>');
    setPassword('TestPassword123!');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-blue-600">
            超简单登录测试 - 无跳转版本
          </h1>
          
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">说明</h2>
            <p className="text-blue-700">
              这个页面只测试登录API，成功后不会跳转到任何页面，避免 ERR_CONNECTION_REFUSED 错误。
            </p>
          </div>

          {/* 输入表单 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 快速选择按钮 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={testGmail}
              className="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
            >
              📧 使用 Gmail 账户
            </button>
            
            <button
              onClick={testHotmail}
              className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
            >
              📧 使用 Hotmail 账户
            </button>
            
            <button
              onClick={testLogin}
              disabled={isLoading}
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isLoading ? '🔄 登录中...' : '🔐 测试登录'}
            </button>
          </div>

          {/* 一键测试 */}
          <div className="mb-6">
            <button
              onClick={async () => {
                testGmail();
                setTimeout(testLogin, 100);
              }}
              disabled={isLoading}
              className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50 text-lg font-semibold"
            >
              {isLoading ? '🔄 测试中...' : '🚀 一键测试 Gmail 登录'}
            </button>
          </div>

          {/* 结果显示 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm min-h-64">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            <pre className="whitespace-pre-wrap break-words">
              {result || '点击上方按钮开始测试...'}
            </pre>
          </div>

          {/* 清除按钮 */}
          <div className="mt-4 text-center">
            <button
              onClick={() => setResult('')}
              className="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
            >
              🗑️ 清除结果
            </button>
          </div>

          {/* 重要提示 */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">重要提示</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p><strong>无跳转设计</strong>: 这个页面登录成功后不会跳转，避免连接被拒绝的问题</p>
              <p><strong>测试账户</strong>: <EMAIL> 和 <EMAIL></p>
              <p><strong>统一密码</strong>: TestPassword123!</p>
              <p><strong>如果还有问题</strong>: 检查浏览器控制台的网络请求</p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a href="/login-fix-test" className="text-blue-600 hover:text-blue-800 underline">
              登录修复测试
            </a>
            <a href="/final-auth-test" className="text-blue-600 hover:text-blue-800 underline">
              终极认证测试
            </a>
            <a href="/" className="text-blue-600 hover:text-blue-800 underline">
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
