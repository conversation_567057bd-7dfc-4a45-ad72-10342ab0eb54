'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

export interface PageTransitionProps {
  children: ReactNode;
  type?: 'fade' | 'slide' | 'scale' | 'blur' | 'flip';
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
  className?: string;
}

export function PageTransition({
  children,
  type = 'fade',
  direction = 'up',
  duration = 300,
  delay = 0,
  className
}: PageTransitionProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  // 基础过渡样式
  const baseStyles = 'transition-all ease-out';

  // 过渡类型样式
  const getTransitionStyles = () => {
    const styles = {
      opacity: isVisible ? 1 : 0,
      transitionDuration: `${duration}ms`
    };

    switch (type) {
      case 'fade':
        return styles;

      case 'slide':
        const slideTransforms = {
          up: isVisible ? 'translateY(0)' : 'translateY(20px)',
          down: isVisible ? 'translateY(0)' : 'translateY(-20px)',
          left: isVisible ? 'translateX(0)' : 'translateX(20px)',
          right: isVisible ? 'translateX(0)' : 'translateX(-20px)'
        };
        return {
          ...styles,
          transform: slideTransforms[direction]
        };

      case 'scale':
        return {
          ...styles,
          transform: isVisible ? 'scale(1)' : 'scale(0.95)'
        };

      case 'blur':
        return {
          ...styles,
          filter: isVisible ? 'blur(0px)' : 'blur(4px)'
        };

      case 'flip':
        return {
          ...styles,
          transform: isVisible ? 'rotateY(0deg)' : 'rotateY(90deg)',
          transformStyle: 'preserve-3d' as const
        };

      default:
        return styles;
    }
  };

  return (
    <div
      className={cn(baseStyles, className)}
      style={getTransitionStyles()}
    >
      {children}
    </div>
  );
}

// 交错动画组件
export function StaggeredTransition({
  children,
  staggerDelay = 100,
  ...transitionProps
}: PageTransitionProps & {
  staggerDelay?: number;
}) {
  const childrenArray = React.Children.toArray(children);

  return (
    <>
      {childrenArray.map((child, index) => (
        <PageTransition
          key={index}
          {...transitionProps}
          delay={(transitionProps.delay || 0) + index * staggerDelay}
        >
          {child}
        </PageTransition>
      ))}
    </>
  );
}

// 视口进入动画组件
export function InViewTransition({
  children,
  threshold = 0.1,
  rootMargin = '0px',
  ...transitionProps
}: PageTransitionProps & {
  threshold?: number;
  rootMargin?: string;
}) {
  const [isInView, setIsInView] = useState(false);
  const [ref, setRef] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!ref) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.unobserve(ref);
        }
      },
      { threshold, rootMargin }
    );

    observer.observe(ref);

    return () => observer.disconnect();
  }, [ref, threshold, rootMargin]);

  return (
    <div ref={setRef}>
      <PageTransition
        {...transitionProps}
        delay={isInView ? transitionProps.delay : 0}
      >
        <div style={{ opacity: isInView ? 1 : 0 }}>
          {children}
        </div>
      </PageTransition>
    </div>
  );
}

// 路由过渡包装器
export function RouteTransition({
  children,
  className
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <PageTransition
      type="fade"
      duration={200}
      className={cn('min-h-screen', className)}
    >
      {children}
    </PageTransition>
  );
}
