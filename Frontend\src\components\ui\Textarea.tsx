'use client';

import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outlined' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({
    className,
    label,
    error,
    helperText,
    variant = 'default',
    size = 'md',
    fullWidth = false,
    resize = 'vertical',
    autoResize = false,
    disabled,
    ...props
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false);
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);

    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setHasValue(!!e.target.value);
      
      // Auto resize functionality
      if (autoResize) {
        e.target.style.height = 'auto';
        e.target.style.height = `${e.target.scrollHeight}px`;
      }
      
      props.onChange?.(e);
    };

    // 基础样式
    const baseStyles = [
      'w-full transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-offset-1',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      fullWidth && 'w-full'
    ];

    // 变体样式
    const variantStyles = {
      default: [
        'bg-surface border-2 border-border',
        'focus:border-primary focus:ring-primary/20',
        error ? 'border-error focus:border-error focus:ring-error/20' : '',
        'hover:border-primary/50'
      ],
      filled: [
        'bg-surface-2 border-0',
        'focus:bg-surface focus:ring-primary/20',
        error ? 'focus:ring-error/20' : '',
        'hover:bg-surface'
      ],
      outlined: [
        'bg-transparent border-2 border-border',
        'focus:border-primary focus:ring-primary/20',
        error ? 'border-error focus:border-error focus:ring-error/20' : '',
        'hover:border-primary/50'
      ],
      glass: [
        'bg-white/10 backdrop-blur-md border border-white/20',
        'focus:bg-white/20 focus:ring-primary/20',
        error ? 'border-error/50 focus:border-error focus:ring-error/20' : '',
        'hover:bg-white/15'
      ]
    };

    // 尺寸样式
    const sizeStyles = {
      sm: 'px-3 py-2 text-sm min-h-[80px]',
      md: 'px-4 py-3 text-base min-h-[100px]',
      lg: 'px-5 py-4 text-lg min-h-[120px]'
    };

    // 调整大小样式
    const resizeStyles = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize'
    };

    // 圆角样式
    const radiusStyles = 'rounded-xl';

    // 标签样式
    const labelStyles = cn(
      'absolute left-4 transition-all duration-300 pointer-events-none',
      'text-text-secondary',
      isFocused || hasValue
        ? 'top-2 text-xs font-medium'
        : size === 'sm'
        ? 'top-2 text-sm'
        : size === 'md'
        ? 'top-3 text-base'
        : 'top-4 text-lg',
      isFocused && !error && 'text-primary',
      error && 'text-error'
    );

    const textareaStyles = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      resizeStyles[resize],
      radiusStyles,
      label && 'pt-6 pb-2',
      'text-text-primary placeholder-text-muted',
      className
    );

    return (
      <div className={cn('relative', fullWidth && 'w-full')}>
        {/* 文本域容器 */}
        <div className="relative">
          {/* 文本域 */}
          <textarea
            ref={ref}
            className={textareaStyles}
            disabled={disabled}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            placeholder={label ? '' : props.placeholder}
            {...props}
          />

          {/* 浮动标签 */}
          {label && (
            <label className={labelStyles}>
              {label}
            </label>
          )}
        </div>

        {/* 错误信息或帮助文本 */}
        {(error || helperText) && (
          <div className="mt-2 px-1">
            {error ? (
              <p className="text-sm text-error animate-fade-in">{error}</p>
            ) : (
              <p className="text-sm text-text-muted">{helperText}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export { Textarea };
