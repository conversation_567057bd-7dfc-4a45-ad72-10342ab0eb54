'use client';

import React from 'react';

interface Category {
  id: string;
  name: string;
}

interface CategorySidebarProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  className?: string;
}

const categories: Category[] = [
  { id: 'trending', name: 'Trending' },
  { id: 'technology', name: 'Technology' },
  { id: 'lifestyle', name: 'Lifestyle' },
  { id: 'travel', name: 'Travel' },
  { id: 'food', name: 'Food' },
  { id: 'finance', name: 'Finance' },
  { id: 'entertainment', name: 'Entertainment' },
  { id: 'politics', name: 'Politics' },
  { id: 'science', name: 'Science' },
  { id: 'health', name: 'Health' },
  { id: 'sports', name: 'Sports' },
  { id: 'business', name: 'Business' }
];

export default function CategorySidebar({
  selectedCategory,
  onCategoryChange,
  className
}: CategorySidebarProps) {
  return (
    <aside className={`w-64 bg-white h-screen sticky top-0 overflow-y-auto ${className || ''}`}>
      <div className="p-6">
        <nav className="space-y-1">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`w-full flex items-center px-3 py-2 text-left transition-colors duration-200 rounded-md text-sm ${
                selectedCategory === category.id
                  ? 'bg-blue-50 text-blue-600 font-medium'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              {category.name}
            </button>
          ))}
        </nav>
      </div>
    </aside>
  );
}


