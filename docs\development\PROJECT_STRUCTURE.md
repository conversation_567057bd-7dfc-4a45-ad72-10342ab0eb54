# OneNews 项目结构说明

## 📁 根目录结构

```
OneNews/
├── 📁 Backend/                 # 后端服务 (Node.js + Express)
├── 📁 Frontend/                # 前端应用 (Next.js + React)
├── 📁 config/                  # 项目配置文件
├── 📁 docs/                    # 项目文档
├── 📁 scripts/                 # 项目管理脚本
├── 📁 tests/                   # 测试文件
├── 📁 tools/                   # 开发工具
├── 📄 start.ps1               # 快速启动脚本
├── 📄 README.md               # 项目说明
├── 📄 PROJECT_STATUS.md       # 项目状态
└── 📄 PROJECT_STRUCTURE.md    # 本文件
```

## 📋 目录详细说明

### 🔧 config/ - 配置文件目录
- `docker-compose.yml` - 生产环境 Docker 配置
- `docker-compose.dev.yml` - 开发环境 Docker 配置
- `.env.example` - 环境变量配置示例

### 📜 scripts/ - 项目管理脚本
- `manage.ps1` - 统一的项目管理工具
  - 启动/停止项目
  - 清理项目文件
  - 验证项目状态
  - Docker 环境管理

### 🏗️ Backend/ - 后端服务
```
Backend/
├── 📁 config/          # 后端配置
├── 📁 middleware/      # 中间件
├── 📁 models/          # 数据模型
├── 📁 routes/          # API 路由
├── 📁 services/        # 业务服务
├── 📁 logs/            # 日志文件
├── 📁 uploads/         # 文件上传
└── 📄 server.js        # 服务器入口
```

### 🎨 Frontend/ - 前端应用
```
Frontend/
├── 📁 src/
│   ├── 📁 app/         # Next.js App Router
│   ├── 📁 components/  # React 组件
│   ├── 📁 contexts/    # React Context
│   ├── 📁 hooks/       # 自定义 Hooks
│   ├── 📁 lib/         # 工具库
│   └── 📁 styles/      # 样式文件
├── 📁 public/          # 静态资源
└── 📄 package.json     # 前端依赖
```

### 📚 docs/ - 项目文档
- `README.md` - 文档总览
- `setup/` - 安装配置指南
- `deployment/` - 部署指南

### 🧪 tests/ - 测试文件
- `integration/` - 集成测试
- `debug/` - 调试工具
- `data/` - 测试数据

### 🛠️ tools/ - 开发工具
- `docker/` - Docker 相关工具
- `nginx/` - Nginx 配置
- `scripts/` - 辅助脚本

## 🚀 快速开始

### 1. 使用快速启动脚本
```powershell
# 启动本地开发环境
.\start.ps1

# 启动 Docker 开发环境
.\start.ps1 dev
```

### 2. 使用完整管理工具
```powershell
# 查看所有可用命令
.\scripts\manage.ps1 help

# 启动项目
.\scripts\manage.ps1 start local

# 清理项目
.\scripts\manage.ps1 clean

# 验证项目状态
.\scripts\manage.ps1 verify
```

## 📝 配置说明

### 环境变量配置
1. 复制 `config/.env.example` 到项目根目录并重命名为 `.env`
2. 根据实际情况填写配置值
3. 前端环境变量需要在 `Frontend/.env.local` 中配置

### Docker 部署
- 开发环境: `docker-compose -f config/docker-compose.dev.yml up`
- 生产环境: `docker-compose -f config/docker-compose.yml up`

## 🔍 项目特性

### ✅ 已实现功能
- 🔐 完整的用户认证系统
- 📱 响应式前端界面
- 🗄️ PostgreSQL 数据库集成
- 🔔 实时通知系统
- 📊 数据分析功能
- 🎯 内容管理系统
- 👥 社交功能
- 🐳 Docker 容器化部署

### 🎯 技术栈
- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Node.js, Express.js, Sequelize ORM
- **数据库**: PostgreSQL, Redis
- **认证**: JWT, Passport.js
- **实时通信**: Socket.IO
- **容器化**: Docker, Docker Compose
- **反向代理**: Nginx

## 📞 支持

如有问题，请查看：
1. `README.md` - 基本使用说明
2. `docs/` - 详细文档
3. `PROJECT_STATUS.md` - 项目当前状态
