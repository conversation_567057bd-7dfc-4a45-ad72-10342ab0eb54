'use client';

import { useRouter } from 'next/navigation';
import { Article } from '@/types';
import { Card } from '@/components/ui/Card';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface ArticleCardProps {
  article: Article;
  layout?: 'horizontal' | 'vertical' | 'grid';
  variant?: 'default' | 'featured' | 'minimal';
  showImage?: boolean;
  showAuthor?: boolean;
  showStats?: boolean;
}

const getCategoryConfig = (category: string) => {
  const configs = {
    'Technology': {
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      textColor: 'text-blue-600',
      icon: '💻'
    },
    'Travel': {
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      textColor: 'text-purple-600',
      icon: '✈️'
    },
    'Lifestyle': {
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      textColor: 'text-green-600',
      icon: '🌱'
    },
    'Food': {
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      textColor: 'text-orange-600',
      icon: '🍽️'
    },
    'Trending': {
      color: 'bg-gradient-to-r from-red-500 to-red-600',
      textColor: 'text-red-600',
      icon: '🔥'
    },
  };
  return configs[category as keyof typeof configs] || {
    color: 'bg-gradient-to-r from-neutral-500 to-neutral-600',
    textColor: 'text-neutral-600',
    icon: '📄'
  };
};

export default function ArticleCard({
  article,
  layout = 'horizontal',
  variant = 'default',
  showImage = true,
  showAuthor = true,
  showStats = true
}: ArticleCardProps) {
  const router = useRouter();
  const [imageError, setImageError] = useState(false);
  const categoryConfig = getCategoryConfig(article.category);

  const handleClick = () => {
    router.push(`/article/${article.id}`);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  // 格式化阅读时间
  const formatReadTime = (minutes: number) => {
    return `${minutes} min read`;
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  if (layout === 'vertical') {
    return (
      <Card
        variant="glass"
        hover="lift"
        padding="none"
        radius="2xl"
        onClick={handleClick}
        className="cursor-pointer group overflow-hidden animate-fade-in"
      >
        {/* Image */}
        {showImage && (
          <div className="relative h-48 overflow-hidden">
            <img
              src={imageError ? `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=667eea&color=fff&size=400x200` : article.image}
              alt={article.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              onError={handleImageError}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Category Badge */}
            <div className="absolute top-4 left-4">
              <span className={cn(
                "inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-semibold text-white shadow-lg",
                categoryConfig.color
              )}>
                <span>{categoryConfig.icon}</span>
                {article.category}
              </span>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {/* Title */}
          <h3 className="text-lg font-bold text-text-primary mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-300">
            {article.title}
          </h3>

          {/* Description */}
          <p className="text-text-secondary text-sm leading-relaxed line-clamp-3 mb-4">
            {article.description}
          </p>

          {/* Author & Stats */}
          <div className="flex items-center justify-between">
            {showAuthor && (
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-gradient-primary flex items-center justify-center">
                  <span className="text-white text-xs font-semibold">
                    {typeof article.author === 'string'
                      ? article.author.charAt(0).toUpperCase()
                      : article.author.name.charAt(0).toUpperCase()
                    }
                  </span>
                </div>
                <div>
                  <p className="text-xs font-medium text-text-primary">
                    {typeof article.author === 'string' ? article.author : article.author.name}
                  </p>
                  <p className="text-xs text-text-muted">
                    {formatReadTime(article.readTime)}
                  </p>
                </div>
              </div>
            )}

            {showStats && (
              <div className="flex items-center gap-4 text-xs text-text-muted">
                <span className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  {formatNumber(article.views)}
                </span>
                <span className="flex items-center gap-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  {formatNumber(article.likes)}
                </span>
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  }

  // Horizontal layout (default)
  return (
    <div
      onClick={handleClick}
      className="cursor-pointer group bg-white rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
    >
      <div className="flex gap-6">
        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Category */}
          <div className="mb-3">
            <span className="text-sm text-blue-600 font-medium">
              {article.category}
            </span>
          </div>

          {/* Title */}
          <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {article.title}
          </h2>

          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 mb-4">
            {article.description}
          </p>

          {/* Author & Stats */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  {typeof article.author === 'string'
                    ? article.author.charAt(0).toUpperCase()
                    : article.author.name.charAt(0).toUpperCase()
                  }
                </span>
              </div>
              <span>
                {typeof article.author === 'string' ? article.author : article.author.name}
              </span>
            </div>
            <span>{formatReadTime(article.readTime)}</span>
          </div>
        </div>

        {/* Image */}
        {showImage && (
          <div className="w-64 h-40 flex-shrink-0">
            <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-200">
              <img
                src={imageError ? `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=667eea&color=fff&size=400x200` : article.image}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={handleImageError}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
