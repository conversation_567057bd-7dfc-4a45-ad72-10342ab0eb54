# Newzora - 全球创作者平台

<div align="center">

![Newzora Logo](https://via.placeholder.com/200x80/4F46E5/FFFFFF?text=Newzora)

**一个现代化的全球创作者内容分享平台**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-22+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-blue.svg)](https://typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-black.svg)](https://nextjs.org/)
[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)](https://github.com)
[![Code Quality](https://img.shields.io/badge/Code%20Quality-Excellent-brightgreen.svg)](https://github.com)

</div>

## 🌟 项目简介

Newzora 是一个功能丰富的全球创作者平台，支持文章、音频、视频等多种内容形式的创作和分享。平台提供完整的社交功能、收益系统和内容管理工具，为创作者提供一站式的内容创作和变现解决方案。

## ✨ 核心功能

### 🔐 用户系统
- **多种登录方式**: 邮箱注册、社交媒体登录 (Google, Facebook, X, Apple)
- **安全认证**: JWT Token、双因子认证、密码加密
- **用户管理**: 个人资料、隐私设置、账户验证

### 📝 内容创作
- **多媒体创作**: 支持文章、视频、音频三种内容类型
- **富文本编辑器**: 现代化的编辑体验，支持 Markdown
- **高清媒体支持**: 视频(MP4/AVI/MOV)、音频(MP3/WAV/AAC)、高清图片
- **草稿系统**: 自动保存、版本控制、协作编辑
- **分类标签**: 灵活的内容分类和标签管理

### 🤖 AI智能审核
- **内容安全**: 政治敏感、血腥暴力、虚假信息自动检测
- **质量评估**: 内容质量智能评估和改进建议
- **实时反馈**: 即时审核结果和优化建议
- **多维检测**: 仇恨言论、色情内容、原创性检测

### 📊 创作助手
- **热点趋势**: 实时热门话题和创作趋势分析
- **创作建议**: 智能标题优化、标签推荐、内容建议
- **数据洞察**: 浏览量趋势、用户行为分析
- **创作技巧**: 专业创作指导和最佳实践

### 🤝 社交互动
- **关注系统**: 关注创作者，构建社交网络
- **评论回复**: 多层级评论、实时互动
- **点赞分享**: 内容互动、社交分享
- **通知系统**: 实时通知、消息推送

### 💰 收益系统
- **打赏功能**: 读者打赏、虚拟礼品
- **广告收益**: 内容广告、收益分成
- **订阅付费**: 付费内容、会员系统
- **提现管理**: 多种提现方式、税务处理

### 🔍 发现功能
- **智能推荐**: AI 算法推荐相关内容
- **搜索引擎**: 全文搜索、分类筛选
- **热门排行**: 实时热门内容排行
- **个性化**: 基于用户行为的个性化推荐

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 14 + React 18
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: React Context + Hooks
- **构建工具**: Webpack + Turbopack

### 后端技术栈
- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: PostgreSQL + Sequelize ORM
- **认证**: JWT + Passport.js
- **文件存储**: 本地存储 + 云存储支持

### 部署架构
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2
- **监控**: 日志系统 + 性能监控

## 🚀 快速开始

### 环境要求
- Node.js 18.0+
- PostgreSQL 13.0+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/newzora.git
   cd newzora
   ```

2. **安装依赖**
   ```bash
   # 安装后端依赖
   cd Backend
   npm install
   
   # 安装前端依赖
   cd ../Frontend
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp Backend/.env.example Backend/.env
   
   # 编辑配置文件
   nano Backend/.env
   ```

4. **初始化数据库**
   ```bash
   cd Backend
   npm run db:migrate
   npm run db:seed
   ```

5. **启动服务**
   ```bash
   # 启动前端服务 (端口 3000)
   cd Frontend
   npm run dev
   ```

6. **访问应用**
   - 前端应用: http://localhost:3000
   - 所有功能已集成到前端，无需单独启动后端

### 🧪 测试账户
平台提供完整的测试账户系统，登录页面会显示可用的测试账户：

- **技术作家**: <EMAIL> / password123
- **旅行博主**: <EMAIL> / password123
- **金融分析师**: <EMAIL> / password123
- **生活方式博主**: <EMAIL> / password123
- **平台管理员**: <EMAIL> / admin123

> 💡 提示：登录页面提供一键填充测试账户功能，方便快速测试

## 📁 项目结构

```
Newzora/
├── Backend/                 # 后端应用
│   ├── config/             # 数据库配置
│   ├── middleware/         # 中间件
│   ├── models/            # 数据模型
│   ├── routes/            # API 路由
│   ├── services/          # 业务逻辑
│   └── server.js          # 服务器入口
├── Frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # React 组件
│   │   └── contexts/      # React Context
│   └── public/            # 静态资源
├── deployment/            # 部署配置
├── docs/                  # 项目文档
├── tools/                 # 开发工具
├── 功能测试报告.md         # 功能测试报告
├── 项目进度表.md          # 项目进度表
├── 更新日志.md            # 更新日志
└── README.md             # 项目说明
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd Backend
npm test

# 前端测试
cd Frontend
npm test

# 端到端测试
npm run test:e2e
```

### 测试覆盖率
- 后端 API: 85%+
- 前端组件: 80%+
- 集成测试: 75%+

## 📊 项目状态

- **开发状态**: 活跃开发中
- **版本**: v1.4.0
- **功能完成度**: 95%
- **测试覆盖率**: 82%

详细进度请查看 [项目进度表](项目进度表.md)

## 📖 文档

- [功能测试报告](功能测试报告.md) - 详细的功能测试结果
- [项目进度表](项目进度表.md) - 开发进度和里程碑
- [更新日志](更新日志.md) - 版本更新记录
- [部署指南](docs/deployment/) - 生产环境部署
- [开发指南](docs/development/) - 开发环境设置

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 贡献方式
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 遵循 ESLint 和 Prettier 配置
- 编写单元测试
- 更新相关文档
- 遵循 Git 提交规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 👥 团队

- **项目负责人**: 开发团队
- **前端开发**: React/Next.js 团队
- **后端开发**: Node.js 团队
- **UI/UX 设计**: 设计团队

## 📞 联系我们

- **项目主页**: https://github.com/your-username/newzora
- **问题反馈**: https://github.com/your-username/newzora/issues
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢所有为 Newzora 项目做出贡献的开发者和用户！

---

<div align="center">
Made with ❤️ by Newzora Team
</div>
