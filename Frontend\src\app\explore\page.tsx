'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import CategorySidebar from '@/components/CategorySidebar';
import WorkCard from '@/components/WorkCard';
import { Work } from '@/types';
import { mockWorks, mockWorksByCategory, featuredWorks, latestWorks } from '@/data/mockWorks';
import { useAuth } from '@/contexts/AuthContext';

export default function ExplorePage() {
  const [works, setWorks] = useState<Work[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('trending');
  const [loading, setLoading] = useState(true);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    loadWorks();
  }, [selectedCategory]);

  const loadWorks = () => {
    setLoading(true);

    // 根据选择的分类加载作品数据
    let filteredWorks: Work[] = [];

    switch (selectedCategory) {
      case 'trending':
        filteredWorks = featuredWorks;
        break;
      case 'all':
        filteredWorks = latestWorks;
        break;
      default:
        filteredWorks = mockWorksByCategory[selectedCategory] || [];
        break;
    }

    // 模拟加载延迟
    setTimeout(() => {
      setWorks(filteredWorks);
      setLoading(false);
    }, 300);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex">
          <CategorySidebar
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
          <main className="flex-1 p-8">
            <div className="mb-6">
              <div className="h-6 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex">
        {/* Category Sidebar */}
        <CategorySidebar
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />

        {/* Main Content */}
        <main className="flex-1 p-8">
          {/* Content Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Explore
            </h1>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">
              {selectedCategory === 'trending' ? 'Featured Content' : `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Works`}
            </h2>
            <p className="text-gray-600">
              Discover the best articles, videos, and audio content from talented creators
            </p>
          </div>

          {/* Works Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {works.length === 0 ? (
              <div className="col-span-full text-center py-16">
                <div className="text-gray-400 text-6xl mb-4">🔍</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
                <p className="text-gray-600">Try selecting a different category or check back later.</p>
              </div>
            ) : (
              works.map((work) => (
                <WorkCard
                  key={work.id}
                  work={work}
                  layout="vertical"
                  showImage={true}
                  showAuthor={true}
                  showStats={true}
                  showInteractions={isAuthenticated}
                />
              ))
            )}
          </div>

          {/* Load More Button */}
          {works.length > 0 && (
            <div className="text-center mt-12">
              <button className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Discover More Content
              </button>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
