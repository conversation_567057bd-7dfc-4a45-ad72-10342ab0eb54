'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'white' | 'dark';
  showText?: boolean;
  className?: string;
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-10 h-10',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16'
};

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl',
  xl: 'text-3xl'
};

export default function Logo({ 
  size = 'md', 
  variant = 'default', 
  showText = true, 
  className 
}: LogoProps) {
  const iconSize = sizeClasses[size];
  const textSize = textSizeClasses[size];

  const getColors = () => {
    switch (variant) {
      case 'white':
        return {
          primary: '#ffffff',
          secondary: '#f1f5f9',
          text: 'text-white'
        };
      case 'dark':
        return {
          primary: '#1e293b',
          secondary: '#334155',
          text: 'text-slate-800'
        };
      default:
        return {
          primary: '#3b82f6', // Blue
          secondary: '#8b5cf6', // Purple
          text: 'text-primary'
        };
    }
  };

  const colors = getColors();

  return (
    <div className={cn('flex items-center gap-3', className)}>
      {/* Logo Icon */}
      <div className={cn('relative', iconSize)}>
        <svg
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
        >
          {/* Background Circle */}
          <circle
            cx="20"
            cy="20"
            r="18"
            fill="url(#logoGradient)"
            className="drop-shadow-lg"
          />
          
          {/* Letter N */}
          <path
            d="M12 12v16h2.5v-11.5L22 28h1.5V12H21v11.5L13.5 12H12z"
            fill="white"
            className="drop-shadow-sm"
          />
          
          {/* Accent Dot */}
          <circle
            cx="30"
            cy="14"
            r="3"
            fill={colors.secondary}
            className="animate-pulse"
          />
          
          {/* Gradient Definition */}
          <defs>
            <linearGradient
              id="logoGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor={colors.primary} />
              <stop offset="100%" stopColor={colors.secondary} />
            </linearGradient>
          </defs>
        </svg>
      </div>

      {/* Brand Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={cn(
            'font-bold tracking-tight leading-none',
            textSize,
            colors.text
          )}>
            Newzora
          </span>
          {size === 'lg' || size === 'xl' ? (
            <span className="text-xs text-text-muted font-medium tracking-wide">
              Global News Platform
            </span>
          ) : null}
        </div>
      )}
    </div>
  );
}

// Animated Logo for loading states
export function AnimatedLogo({ size = 'md', className }: Pick<LogoProps, 'size' | 'className'>) {
  const iconSize = sizeClasses[size];

  return (
    <div className={cn('relative', iconSize, className)}>
      <svg
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full animate-spin"
        style={{ animationDuration: '3s' }}
      >
        {/* Rotating Ring */}
        <circle
          cx="20"
          cy="20"
          r="18"
          stroke="url(#animatedGradient)"
          strokeWidth="2"
          fill="none"
          strokeDasharray="20 10"
          className="animate-pulse"
        />
        
        {/* Center Logo */}
        <circle
          cx="20"
          cy="20"
          r="12"
          fill="url(#centerGradient)"
        />
        
        <path
          d="M15 15v10h1.5v-7L21 25h1V15h-1.5v7L16 15H15z"
          fill="white"
        />
        
        <defs>
          <linearGradient id="animatedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="50%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#06b6d4" />
          </linearGradient>
          <linearGradient id="centerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#1e40af" />
            <stop offset="100%" stopColor="#7c3aed" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
}

// Minimal Logo for small spaces
export function MinimalLogo({ size = 'sm', className }: Pick<LogoProps, 'size' | 'className'>) {
  const iconSize = sizeClasses[size];

  return (
    <div className={cn('relative', iconSize, className)}>
      <div className="w-full h-full bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center shadow-lg">
        <span className="text-white font-bold text-sm">N</span>
      </div>
    </div>
  );
}

// Logo with notification badge
export function LogoWithBadge({ 
  size = 'md', 
  badgeCount, 
  className 
}: Pick<LogoProps, 'size' | 'className'> & { badgeCount?: number }) {
  return (
    <div className={cn('relative', className)}>
      <Logo size={size} showText={false} />
      {badgeCount && badgeCount > 0 && (
        <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
          {badgeCount > 99 ? '99+' : badgeCount}
        </div>
      )}
    </div>
  );
}
