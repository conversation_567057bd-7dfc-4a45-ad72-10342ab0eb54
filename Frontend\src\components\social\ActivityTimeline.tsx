'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import FollowButton from './FollowButton';
import ShareButton from './ShareButton';
import { 
  UserPlusIcon, 
  HeartIcon, 
  ChatBubbleLeftIcon,
  BookmarkIcon,
  ClockIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface Activity {
  id: number;
  userId: number;
  activityType: 'article_liked' | 'article_commented' | 'article_bookmarked' | 'user_followed' | 'article_shared';
  targetType: 'article' | 'user' | 'comment';
  targetId: number;
  metadata: any;
  createdAt: string;
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
  article?: {
    id: number;
    title: string;
    image?: string;
    category: string;
  };
  targetUser?: {
    id: number;
    username: string;
    avatar?: string;
  };
}

interface ActivityTimelineProps {
  userId?: number; // If provided, show user's activities; otherwise show timeline
  className?: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function ActivityTimeline({ userId, className = '' }: ActivityTimelineProps) {
  const { user, token, isAuthenticated } = useAuth();
  const toast = useToast();
  
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [loadingMore, setLoadingMore] = useState(false);

  const fetchActivities = useCallback(async (page: number = 1, append: boolean = false) => {
    if (!isAuthenticated || !token) {
      setLoading(false);
      return;
    }

    try {
      if (!append) setLoading(true);
      else setLoadingMore(true);

      const endpoint = userId 
        ? `/activities/user/${userId}?page=${page}&limit=20`
        : `/activities/timeline?page=${page}&limit=20`;

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const newActivities = data.data.activities;
          setActivities(prev => append ? [...prev, ...newActivities] : newActivities);
          setPagination(data.data.pagination);
        } else {
          toast.error('Load Failed', data.message || 'Unable to get activities');
        }
      } else if (response.status === 401) {
        toast.error('Authentication Failed', 'Please log in again');
      } else {
        toast.error('Load Failed', 'Server error, please try again later');
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
      toast.error('Load Failed', 'Network error, please check connection');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [userId, isAuthenticated, token, toast]);

  useEffect(() => {
    fetchActivities(1, false);
  }, [fetchActivities]);

  const handleLoadMore = () => {
    if (pagination.page < pagination.pages && !loadingMore) {
      fetchActivities(pagination.page + 1, true);
    }
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'article_liked':
        return <HeartSolidIcon className="h-5 w-5 text-red-500" />;
      case 'article_commented':
        return <ChatBubbleLeftIcon className="h-5 w-5 text-blue-500" />;
      case 'article_bookmarked':
        return <BookmarkIcon className="h-5 w-5 text-yellow-500" />;
      case 'user_followed':
        return <UserPlusIcon className="h-5 w-5 text-green-500" />;
      case 'article_shared':
        return <ShareButton articleId={0} title="" variant="icon" size="sm" className="pointer-events-none" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getActivityText = (activity: Activity) => {
    switch (activity.activityType) {
      case 'article_liked':
        return 'liked article';
      case 'article_commented':
        return 'commented on article';
      case 'article_bookmarked':
        return 'bookmarked article';
      case 'user_followed':
        return 'followed';
      case 'article_shared':
        return 'shared article';
      default:
        return 'performed action';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAuthenticated) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Please log in first</h3>
        <p className="mt-2 text-sm text-gray-500">Log in to view activity timeline</p>
        <Link
          href="/auth/login"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          Log in now
        </Link>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[...Array(5)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg border p-6 animate-pulse">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-3">
                <div className="flex items-center space-x-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-12"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (activities.length === 0) {
    const emptyMessage = userId
      ? 'This user has no activities yet'
      : 'No activities yet, follow some users to see their activities!';
    
    return (
      <div className={`text-center py-12 ${className}`}>
        <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">No Activities</h3>
        <p className="mt-2 text-sm text-gray-500">{emptyMessage}</p>
        {!userId && (
          <Link
            href="/explore"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Discover Users
          </Link>
        )}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {activities.map((activity) => (
        <div key={activity.id} className="bg-white rounded-lg border p-6 hover:shadow-md transition-shadow">
          <div className="flex items-start space-x-4">
            {/* User Avatar */}
            <Link href={`/profile/${activity.user.id}`} className="flex-shrink-0">
              <div className="relative w-10 h-10 rounded-full overflow-hidden bg-gray-100">
                {activity.user.avatar ? (
                  <Image
                    src={activity.user.avatar}
                    alt={activity.user.username}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-400 to-purple-500">
                    <span className="text-white font-medium text-sm">
                      {activity.user.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
            </Link>

            <div className="flex-1 min-w-0">
              {/* Activity Header */}
              <div className="flex items-center space-x-2 mb-2">
                <Link 
                  href={`/profile/${activity.user.id}`}
                  className="font-medium text-gray-900 hover:text-blue-600 transition-colors"
                >
                  {activity.user.username}
                </Link>
                <div className="flex items-center space-x-1">
                  {getActivityIcon(activity.activityType)}
                  <span className="text-sm text-gray-600">{getActivityText(activity)}</span>
                </div>
                <span className="text-xs text-gray-400">{formatTime(activity.createdAt)}</span>
              </div>

              {/* Activity Content */}
              {activity.article && (
                <Link href={`/article/${activity.article.id}`} className="block">
                  <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    {activity.article.image && (
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={activity.article.image}
                          alt={activity.article.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
                        {activity.article.title}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {activity.article.category}
                      </p>
                    </div>
                  </div>
                </Link>
              )}

              {activity.targetUser && activity.activityType === 'user_followed' && (
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <Link href={`/profile/${activity.targetUser.id}`} className="flex items-center space-x-3">
                    <div className="relative w-8 h-8 rounded-full overflow-hidden bg-gray-100">
                      {activity.targetUser.avatar ? (
                        <Image
                          src={activity.targetUser.avatar}
                          alt={activity.targetUser.username}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-400 to-blue-500">
                          <span className="text-white font-medium text-xs">
                            {activity.targetUser.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {activity.targetUser.username}
                    </span>
                  </Link>
                  <FollowButton
                    userId={activity.targetUser.id}
                    username={activity.targetUser.username}
                    size="sm"
                    variant="outline"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Load More Button */}
      {pagination.page < pagination.pages && (
        <div className="text-center pt-4">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loadingMore ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                Loading...
              </>
            ) : (
              `Load more activities (${pagination.total - activities.length} remaining)`
            )}
          </button>
        </div>
      )}
    </div>
  );
}
