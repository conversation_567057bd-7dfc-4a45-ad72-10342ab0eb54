'use client';

import React, { useState } from 'react';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

export default function TestRealAccountsPage() {
  const { login, register, logout, user, isAuthenticated, error } = useSupabaseAuth();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 测试账户列表
  const testAccounts = [
    { email: '<EMAIL>', password: 'TestPassword123!', name: 'Gmail 账户' },
    { email: '<EMAIL>', password: 'TestPassword123!', name: 'Hotmail 账户' }
  ];

  // 测试登录功能
  const testLogin = async (email: string, password: string, accountName: string) => {
    setIsLoading(true);
    addResult(`🔐 测试 ${accountName} 登录...`);
    
    try {
      const success = await login(email, password);
      if (success) {
        addResult(`✅ ${accountName} 登录成功！`);
        addResult(`👤 用户信息: ${user?.email || email}`);
      } else {
        addResult(`❌ ${accountName} 登录失败: ${error || '未知错误'}`);
      }
    } catch (err) {
      addResult(`💥 ${accountName} 登录异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试注册功能
  const testRegister = async () => {
    setIsLoading(true);
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    const testUsername = `testuser${Date.now()}`;
    
    addResult(`📝 测试新用户注册: ${testEmail}`);
    
    try {
      const success = await register(testEmail, testPassword, testUsername, 'Test User');
      if (success) {
        addResult(`✅ 新用户注册成功！`);
        addResult(`📧 邮箱: ${testEmail}`);
      } else {
        addResult(`❌ 新用户注册失败: ${error || '未知错误'}`);
      }
    } catch (err) {
      addResult(`💥 注册异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试登出功能
  const testLogout = async () => {
    setIsLoading(true);
    addResult('🚪 测试登出功能...');
    
    try {
      await logout();
      addResult('✅ 登出成功！');
    } catch (err) {
      addResult(`💥 登出异常: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8">真实邮箱账户测试</h1>
          
          {/* 当前认证状态 */}
          <div className="mb-8 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800 mb-4">当前认证状态</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>认证状态:</strong> {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}
              </div>
              <div>
                <strong>用户邮箱:</strong> {user?.email || '无'}
              </div>
              <div>
                <strong>用户ID:</strong> {user?.id || '无'}
              </div>
              <div>
                <strong>错误信息:</strong> {error || '无'}
              </div>
            </div>
            
            {isAuthenticated && (
              <button
                onClick={testLogout}
                disabled={isLoading}
                className="mt-4 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                登出
              </button>
            )}
          </div>

          {/* 测试按钮区域 */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold mb-4">测试功能</h2>
            
            {/* 真实邮箱登录测试 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {testAccounts.map((account, index) => (
                <button
                  key={index}
                  onClick={() => testLogin(account.email, account.password, account.name)}
                  disabled={isLoading}
                  className="bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  测试 {account.name} 登录
                </button>
              ))}
            </div>

            {/* 注册测试 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={testRegister}
                disabled={isLoading}
                className="bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                测试新用户注册
              </button>
              
              <button
                onClick={clearResults}
                disabled={isLoading}
                className="bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 disabled:opacity-50"
              >
                清除测试结果
              </button>
            </div>
          </div>

          {/* 测试结果显示 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            {testResults.length === 0 ? (
              <p className="text-gray-500">点击上方按钮开始测试...</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="break-words">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 测试说明 */}
          <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">测试说明</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <p><strong>真实邮箱账户:</strong></p>
              <ul className="list-disc list-inside ml-4">
                <li><EMAIL> (密码: TestPassword123!)</li>
                <li><EMAIL> (密码: TestPassword123!)</li>
              </ul>
              <p><strong>测试功能:</strong></p>
              <ul className="list-disc list-inside ml-4">
                <li>真实邮箱登录验证</li>
                <li>新用户注册功能</li>
                <li>登出功能测试</li>
                <li>认证状态管理</li>
              </ul>
              <p><strong>注意:</strong> 当前使用模拟认证系统，真实邮箱已预置在系统中</p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a
              href="/auth/supabase-login"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Supabase 登录页面
            </a>
            <a
              href="/auth/supabase-register"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Supabase 注册页面
            </a>
            <a
              href="/"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
