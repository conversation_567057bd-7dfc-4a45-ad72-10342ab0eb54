'use client';

import { useState, useRef } from 'react';
import {
  LinkIcon,
  PhotoIcon,
  CodeBracketIcon,
  ChatBubbleLeftIcon as QuoteIcon
} from '@heroicons/react/24/outline';

// Custom icon components (not available in Heroicons)
const BoldIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6z" />
  </svg>
);

const ItalicIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 4l4 16m-4-8h8" />
  </svg>
);

const UnderlineIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 4v8a4 4 0 008 0V4m-8 15h8" />
  </svg>
);

const ListBulletIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
);

const NumberedListIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h18M3 12h18m-9 8h9" />
  </svg>
);

// 未使用的图标组件已删除以修复ESLint警告



interface RichTextEditorProps {
  value: string;
  onChange: (value: string, html: string) => void;
  placeholder?: string;
  className?: string;
}

export default function RichTextEditor({ 
  value, 
  onChange, 
  placeholder = "Start writing...",
  className = ""
}: RichTextEditorProps) {
  const [isPreview, setIsPreview] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const insertText = (before: string, after: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);
    
    onChange(newText, convertToHtml(newText));
    
    // Restore cursor position
    setTimeout(() => {
      if (textarea) {
        textarea.focus();
        textarea.setSelectionRange(start + before.length, end + before.length);
      }
    }, 0);
  };

  const handleBold = () => insertText('**', '**');
  const handleItalic = () => insertText('*', '*');
  const handleUnderline = () => insertText('<u>', '</u>');
  const handleCode = () => insertText('`', '`');
  const handleQuote = () => insertText('\n> ', '');
  const handleBulletList = () => insertText('\n- ', '');
  const handleNumberedList = () => insertText('\n1. ', '');
  
  const handleLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      const text = prompt('Enter link text:') || url;
      insertText(`[${text}](${url})`);
    }
  };

  const handleImage = () => {
    const url = prompt('Enter image URL:');
    if (url) {
      const alt = prompt('Enter alt text:') || 'Image';
      insertText(`![${alt}](${url})`);
    }
  };

  const handleHeading = (level: number) => {
    const prefix = '#'.repeat(level) + ' ';
    insertText('\n' + prefix, '');
  };

  // Simple markdown to HTML conversion
  const convertToHtml = (markdown: string): string => {
    let html = markdown;
    
    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
    
    // Bold
    html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
    
    // Italic
    html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');
    
    // Code
    html = html.replace(/`(.*)`/gim, '<code>$1</code>');
    
    // Links
    html = html.replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Images
    html = html.replace(/!\[([^\]]*)\]\(([^\)]*)\)/gim, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg" />');
    
    // Line breaks
    html = html.replace(/\n/gim, '<br>');
    
    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-gray-300 pl-4 italic">$1</blockquote>');
    
    // Bullet lists
    html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');
    html = html.replace(/(<li>.*<\/li>)/gim, '<ul class="list-disc list-inside">$1</ul>');
    
    // Numbered lists
    html = html.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
    
    return html;
  };

  const renderPreview = () => {
    const html = convertToHtml(value);
    return (
      <div 
        className="prose max-w-none p-4 min-h-[400px] bg-white border border-gray-300 rounded-lg"
        dangerouslySetInnerHTML={{ __html: html }}
      />
    );
  };

  const toolbarButtons = [
    { icon: BoldIcon, action: handleBold, title: 'Bold (Ctrl+B)', shortcut: 'Ctrl+B' },
    { icon: ItalicIcon, action: handleItalic, title: 'Italic (Ctrl+I)', shortcut: 'Ctrl+I' },
    { icon: UnderlineIcon, action: handleUnderline, title: 'Underline', shortcut: '' },
    { icon: CodeBracketIcon, action: handleCode, title: 'Code', shortcut: '' },
    { icon: QuoteIcon, action: handleQuote, title: 'Quote', shortcut: '' },
    { icon: ListBulletIcon, action: handleBulletList, title: 'Bullet List', shortcut: '' },
    { icon: NumberedListIcon, action: handleNumberedList, title: 'Numbered List', shortcut: '' },
    { icon: LinkIcon, action: handleLink, title: 'Link', shortcut: '' },
    { icon: PhotoIcon, action: handleImage, title: 'Image', shortcut: '' },
  ];

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleBold();
          break;
        case 'i':
          e.preventDefault();
          handleItalic();
          break;
        case 'k':
          e.preventDefault();
          handleLink();
          break;
      }
    }
  };

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2">
        <div className="flex items-center space-x-1">
          {/* Heading Buttons */}
          <div className="flex items-center space-x-1 mr-2 pr-2 border-r border-gray-300">
            <button
              type="button"
              onClick={() => handleHeading(1)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title="Heading 1"
            >
              H1
            </button>
            <button
              type="button"
              onClick={() => handleHeading(2)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title="Heading 2"
            >
              H2
            </button>
            <button
              type="button"
              onClick={() => handleHeading(3)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title="Heading 3"
            >
              H3
            </button>
          </div>

          {/* Format Buttons */}
          {toolbarButtons.map((button, index) => (
            <button
              key={index}
              type="button"
              onClick={button.action}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded transition-colors"
              title={button.title}
            >
              <button.icon className="w-4 h-4" />
            </button>
          ))}

          {/* Preview Toggle */}
          <div className="ml-auto">
            <button
              type="button"
              onClick={() => setIsPreview(!isPreview)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                isPreview 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {isPreview ? 'Edit' : 'Preview'}
            </button>
          </div>
        </div>
      </div>

      {/* Editor/Preview Area */}
      <div className="relative">
        {isPreview ? (
          renderPreview()
        ) : (
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value, convertToHtml(e.target.value))}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="w-full p-4 border-0 resize-none focus:outline-none focus:ring-0 font-mono text-sm"
            rows={20}
            style={{ minHeight: '400px' }}
          />
        )}
      </div>

      {/* Footer with shortcuts */}
      <div className="bg-gray-50 border-t border-gray-300 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>Markdown supported</span>
            <span>•</span>
            <span>{value.length} characters</span>
            <span>•</span>
            <span>{value.split(/\s+/).filter(word => word.length > 0).length} words</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span>Ctrl+B: Bold</span>
            <span>Ctrl+I: Italic</span>
            <span>Ctrl+K: Link</span>
          </div>
        </div>
      </div>
    </div>
  );
}
