'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  email: string;
  username: string;
  display_name?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, username: string, displayName?: string) => Promise<boolean>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE = 'http://localhost:5000/api';

export function SimpleBackendAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const isAuthenticated = !!user;

  // 登录函数
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      
      console.log('🔐 开始登录:', email);
      
      const response = await fetch(`${API_BASE}/users/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password: password
        })
      });

      const data = await response.json();
      console.log('📡 登录响应:', data);

      if (response.ok && data.success) {
        const userData = {
          id: data.user.id,
          email: data.user.email,
          username: data.user.username,
          display_name: data.user.display_name
        };

        setUser(userData);

        // 保存token和用户数据到localStorage
        if (data.token) {
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('auth_user', JSON.stringify(userData));
        }

        console.log('✅ 登录成功');
        return true;
      } else {
        setError(data.message || '登录失败');
        console.log('❌ 登录失败:', data.message);
        return false;
      }
    } catch (err: any) {
      console.error('💥 登录异常:', err);
      setError(err.message || '网络错误');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册函数
  const register = async (email: string, password: string, username: string, displayName?: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError('');
      
      console.log('📝 开始注册:', email);
      
      const response = await fetch(`${API_BASE}/users/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          password: password,
          username: username,
          displayName: displayName
        })
      });

      const data = await response.json();
      console.log('📡 注册响应:', data);

      if (response.ok && data.success) {
        console.log('✅ 注册成功');
        return true;
      } else {
        setError(data.message || '注册失败');
        console.log('❌ 注册失败:', data.message);
        return false;
      }
    } catch (err: any) {
      console.error('💥 注册异常:', err);
      setError(err.message || '网络错误');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 登出函数
  const logout = async (): Promise<void> => {
    setUser(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    console.log('🚪 已登出');
  };

  // 检查是否有保存的token并恢复用户状态
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    const savedUser = localStorage.getItem('auth_user');

    if (token && savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser({
          id: userData.id,
          email: userData.email,
          username: userData.username,
          display_name: userData.display_name
        });
        console.log('� 从localStorage恢复认证状态');
      } catch (error) {
        console.error('❌ 恢复认证状态失败:', error);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
      }
    }
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useSimpleBackendAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSimpleBackendAuth must be used within a SimpleBackendAuthProvider');
  }
  return context;
}
