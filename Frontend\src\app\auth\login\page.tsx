
'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AuthLoginRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    console.log('🔄 /auth/login 页面被访问，重定向到正确的登录页');

    // 防止循环重定向
    if (window.location.pathname === '/auth/login') {
      // 设置一个跳转标记，防止循环
      sessionStorage.setItem('redirected_from_auth_login', 'true');

      // 跳转到正确的登录页
      router.replace('/login');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center">
      <div className="text-center p-8 bg-white shadow-md rounded-lg">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">重定向中...</h1>
        <p className="text-gray-600 mb-6">正在跳转到正确的登录页面</p>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    </div>
  );
}
