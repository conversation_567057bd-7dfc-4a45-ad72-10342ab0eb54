'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ShareButtonProps {
  articleId: number;
  title: string;
  url?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

interface SharePlatform {
  name: string;
  icon: string;
  color: string;
  shareUrl: (url: string, title: string, description?: string) => string;
}

export default function ShareButton({
  articleId,
  title,
  url,
  description = '',
  size = 'md',
  showLabel = true
}: ShareButtonProps) {
  const { token } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [shareCount, setShareCount] = useState(0);

  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : '');
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const encodedUrl = encodeURIComponent(currentUrl);

  const platforms: SharePlatform[] = [
    {
      name: 'Facebook',
      icon: '📘',
      color: 'bg-blue-600 hover:bg-blue-700',
      shareUrl: (url, title) => `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${title}`
    },
    {
      name: 'Twitter',
      icon: '🐦',
      color: 'bg-sky-500 hover:bg-sky-600',
      shareUrl: (url, title) => `https://twitter.com/intent/tweet?url=${url}&text=${title}`
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      color: 'bg-blue-700 hover:bg-blue-800',
      shareUrl: (url, title, desc) => `https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}&summary=${desc || ''}`
    },
    {
      name: 'WhatsApp',
      icon: '💬',
      color: 'bg-green-500 hover:bg-green-600',
      shareUrl: (url, title) => `https://wa.me/?text=${title}%20${url}`
    },
    {
      name: 'Telegram',
      icon: '✈️',
      color: 'bg-blue-500 hover:bg-blue-600',
      shareUrl: (url, title) => `https://t.me/share/url?url=${url}&text=${title}`
    },
    {
      name: 'Email',
      icon: '📧',
      color: 'bg-gray-600 hover:bg-gray-700',
      shareUrl: (url, title, desc) => `mailto:?subject=${title}&body=${desc || ''}%0A%0A${url}`
    }
  ];

  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  };

  const buttonSizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const recordShare = async (platform: string) => {
    try {
      const response = await fetch('http://localhost:5000/api/shares', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        body: JSON.stringify({
          articleId,
          platform: platform.toLowerCase(),
          referrer: typeof window !== 'undefined' ? window.location.href : ''
        })
      });

      if (response.ok) {
        setShareCount(prev => prev + 1);
      }
    } catch (error) {
      console.error('Error recording share:', error);
    }
  };

  const handleShare = async (platform: SharePlatform) => {
    const shareUrl = platform.shareUrl(encodedUrl, encodedTitle, encodedDescription);
    
    // Record the share
    await recordShare(platform.name);
    
    // Open share window
    if (platform.name === 'Email') {
      window.location.href = shareUrl;
    } else {
      window.open(
        shareUrl,
        'share-window',
        'width=600,height=400,scrollbars=yes,resizable=yes'
      );
    }
    
    setIsOpen(false);
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(currentUrl);
      await recordShare('copy_link');
      alert('Link copied to clipboard!');
      setIsOpen(false);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      alert('Failed to copy link');
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url: currentUrl
        });
        await recordShare('native');
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error sharing:', error);
        }
      }
    } else {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleNativeShare}
        className={`flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors ${buttonSizeClasses[size]}`}
        title="Share this article"
      >
        <svg className={sizeClasses[size]} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
        </svg>
        {showLabel && <span>Share</span>}
        {shareCount > 0 && (
          <span className="text-xs text-gray-500">({shareCount})</span>
        )}
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          ></div>
          <div className="absolute bottom-full left-0 mb-2 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-64">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-gray-900 mb-1">Share this article</h3>
              <p className="text-xs text-gray-500 truncate">{title}</p>
            </div>
            
            <div className="grid grid-cols-3 gap-2 mb-3">
              {platforms.map((platform) => (
                <button
                  key={platform.name}
                  onClick={() => handleShare(platform)}
                  className={`flex flex-col items-center p-2 rounded-lg text-white transition-colors ${platform.color}`}
                  title={`Share on ${platform.name}`}
                >
                  <span className="text-lg mb-1">{platform.icon}</span>
                  <span className="text-xs">{platform.name}</span>
                </button>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-3">
              <button
                onClick={copyToClipboard}
                className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-700 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>Copy Link</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
