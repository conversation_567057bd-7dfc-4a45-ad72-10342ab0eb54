'use client';

import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  description?: string;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  indeterminate?: boolean;
}

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({
    className,
    label,
    description,
    error,
    size = 'md',
    variant = 'default',
    indeterminate = false,
    disabled,
    checked,
    onChange,
    ...props
  }, ref) => {
    const [isChecked, setIsChecked] = useState(checked || false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setIsChecked(e.target.checked);
      onChange?.(e);
    };

    // 尺寸样式
    const sizeStyles = {
      sm: {
        checkbox: 'w-4 h-4',
        text: 'text-sm',
        icon: 'w-3 h-3'
      },
      md: {
        checkbox: 'w-5 h-5',
        text: 'text-base',
        icon: 'w-4 h-4'
      },
      lg: {
        checkbox: 'w-6 h-6',
        text: 'text-lg',
        icon: 'w-5 h-5'
      }
    };

    // 变体样式
    const variantStyles = {
      default: [
        'border-2 border-border bg-surface',
        'checked:bg-primary checked:border-primary',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:border-primary/50'
      ],
      filled: [
        'border-0 bg-surface-2',
        'checked:bg-primary',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:bg-surface'
      ],
      outlined: [
        'border-2 border-primary bg-transparent',
        'checked:bg-primary checked:border-primary',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:bg-primary/5'
      ]
    };

    const checkboxStyles = cn(
      'relative cursor-pointer transition-all duration-200',
      'rounded-md appearance-none',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      sizeStyles[size].checkbox,
      variantStyles[variant],
      error && 'border-error focus:ring-error/20',
      className
    );

    return (
      <div className="flex items-start gap-3">
        {/* Checkbox容器 */}
        <div className="relative flex-shrink-0 mt-0.5">
          <input
            ref={ref}
            type="checkbox"
            className={checkboxStyles}
            disabled={disabled}
            checked={checked !== undefined ? checked : isChecked}
            onChange={handleChange}
            {...props}
          />
          
          {/* 自定义复选框图标 */}
          <div className={cn(
            'absolute inset-0 flex items-center justify-center pointer-events-none',
            'text-white transition-all duration-200',
            (checked !== undefined ? checked : isChecked) ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
          )}>
            {indeterminate ? (
              <svg className={sizeStyles[size].icon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M20 12H4" />
              </svg>
            ) : (
              <svg className={sizeStyles[size].icon} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
              </svg>
            )}
          </div>
        </div>

        {/* 标签和描述 */}
        {(label || description) && (
          <div className="flex-1">
            {label && (
              <label className={cn(
                'font-medium cursor-pointer transition-colors',
                sizeStyles[size].text,
                disabled ? 'text-text-muted' : 'text-text-primary',
                error && 'text-error'
              )}>
                {label}
              </label>
            )}
            
            {description && (
              <p className={cn(
                'text-text-muted mt-1',
                size === 'sm' ? 'text-xs' : 'text-sm'
              )}>
                {description}
              </p>
            )}
            
            {error && (
              <p className="text-sm text-error mt-1 animate-fade-in">
                {error}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
