'use client';

import React, { useState } from 'react';
import { SimpleBackendAuthProvider, useSimpleBackendAuth } from '@/contexts/SimpleBackendAuthContext';

function AuthTestContent() {
  const { user, isAuthenticated, isLoading, error, login, register, logout } = useSimpleBackendAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [result, setResult] = useState('');

  const addResult = (message: string) => {
    setResult(prev => prev + `\n${new Date().toLocaleTimeString()}: ${message}`);
  };

  // 测试注册
  const testRegister = async () => {
    const testEmail = email || `test${Date.now()}@example.com`;
    const testPassword = password || 'TestPassword123!';
    const testUsername = username || `user${Date.now()}`;
    
    addResult(`📝 开始注册: ${testEmail}`);
    
    const success = await register(testEmail, testPassword, testUsername, 'Test User');
    
    if (success) {
      addResult('✅ 注册成功！');
      setEmail(testEmail);
      setPassword(testPassword);
      setUsername(testUsername);
    } else {
      addResult(`❌ 注册失败: ${error}`);
    }
  };

  // 测试登录
  const testLogin = async () => {
    const testEmail = email || '<EMAIL>';
    const testPassword = password || 'TestPassword123!';
    
    addResult(`🔐 开始登录: ${testEmail}`);
    
    const success = await login(testEmail, testPassword);
    
    if (success) {
      addResult('✅ 登录成功！');
    } else {
      addResult(`❌ 登录失败: ${error}`);
    }
  };

  // 测试登出
  const testLogout = async () => {
    addResult('🚪 开始登出');
    await logout();
    addResult('✅ 登出成功！');
  };

  // 完整流程测试
  const testFullFlow = async () => {
    setResult('🚀 开始完整流程测试\n');
    
    // 生成测试数据
    const testEmail = `test${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    const testUsername = `user${Date.now()}`;
    
    addResult(`📝 步骤1: 注册新用户 ${testEmail}`);
    const registerSuccess = await register(testEmail, testPassword, testUsername, 'Test User');
    
    if (registerSuccess) {
      addResult('✅ 步骤1: 注册成功！');
      
      // 等待一下
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      addResult('🔐 步骤2: 使用新账户登录');
      const loginSuccess = await login(testEmail, testPassword);
      
      if (loginSuccess) {
        addResult('✅ 步骤2: 登录成功！');
        addResult('🎉 完整流程测试通过！');
      } else {
        addResult(`❌ 步骤2: 登录失败 - ${error}`);
      }
    } else {
      addResult(`❌ 步骤1: 注册失败 - ${error}`);
    }
  };

  // 生成测试数据
  const generateTestData = () => {
    setEmail(`test${Date.now()}@example.com`);
    setPassword('TestPassword123!');
    setUsername(`user${Date.now()}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-green-600">
            最终认证测试 - 直接后端API
          </h1>
          
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h2 className="text-lg font-semibold text-green-800 mb-2">说明</h2>
            <p className="text-green-700">
              这个页面使用最简单的认证系统，直接调用后端API，没有任何复杂的中间层。
              如果这里还不能工作，那就是后端API的问题。
            </p>
          </div>

          {/* 当前状态 */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800 mb-4">当前状态</h2>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div><strong>认证状态:</strong> {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}</div>
              <div><strong>用户邮箱:</strong> {user?.email || '无'}</div>
              <div><strong>用户名:</strong> {user?.username || '无'}</div>
              <div><strong>加载状态:</strong> {isLoading ? '🔄 加载中' : '✅ 空闲'}</div>
              <div><strong>错误信息:</strong> {error || '无'}</div>
              <div><strong>用户ID:</strong> {user?.id || '无'}</div>
            </div>
            
            {isAuthenticated && (
              <button
                onClick={testLogout}
                disabled={isLoading}
                className="mt-4 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                登出
              </button>
            )}
          </div>

          {/* 输入表单 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="TestPassword123!"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="testuser"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button
              onClick={generateTestData}
              disabled={isLoading}
              className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              生成测试数据
            </button>
            
            <button
              onClick={testRegister}
              disabled={isLoading}
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              注册
            </button>
            
            <button
              onClick={testLogin}
              disabled={isLoading}
              className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              登录
            </button>
            
            <button
              onClick={testFullFlow}
              disabled={isLoading}
              className="bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 disabled:opacity-50"
            >
              完整流程测试
            </button>
          </div>

          {/* 快速测试 */}
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">快速测试</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={() => {
                  setEmail('<EMAIL>');
                  setPassword('TestPassword123!');
                  setTimeout(testLogin, 100);
                }}
                disabled={isLoading}
                className="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                测试Gmail账户登录
              </button>
              
              <button
                onClick={() => {
                  setEmail('<EMAIL>');
                  setPassword('TestPassword123!');
                  setTimeout(testLogin, 100);
                }}
                disabled={isLoading}
                className="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                测试Hotmail账户登录
              </button>
            </div>
          </div>

          {/* 结果显示 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm min-h-32">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            <pre className="whitespace-pre-wrap break-words">
              {result || '点击上方按钮开始测试...'}
            </pre>
          </div>

          {/* 清除按钮 */}
          <div className="mt-4 text-center">
            <button
              onClick={() => setResult('')}
              className="bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700"
            >
              清除结果
            </button>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a href="/simple-auth-test" className="text-blue-600 hover:text-blue-800 underline">
              简单API测试
            </a>
            <a href="/test-registration-fix" className="text-blue-600 hover:text-blue-800 underline">
              注册修复测试
            </a>
            <a href="/" className="text-blue-600 hover:text-blue-800 underline">
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function FinalAuthTestPage() {
  return (
    <SimpleBackendAuthProvider>
      <AuthTestContent />
    </SimpleBackendAuthProvider>
  );
}
