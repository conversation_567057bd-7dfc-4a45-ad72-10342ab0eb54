'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import AuthLayout from '@/components/AuthLayout';

export default function GoogleCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const { updateUser } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');

        if (error) {
          setStatus('error');
          setErrorMessage('Google login was cancelled or failed');
          return;
        }

        if (!code) {
          setStatus('error');
          setErrorMessage('Authorization code not received');
          return;
        }

        // Exchange code for tokens
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
        const response = await fetch(`${baseUrl}/users/auth/google/callback?code=${code}`, {
          method: 'GET',
          credentials: 'include',
        });

        const data = await response.json();

        if (data.success) {
          // Store authentication data
          localStorage.setItem('auth_token', data.token);
          localStorage.setItem('auth_user', JSON.stringify(data.user));
          updateUser(data.user);

          setStatus('success');
          
          // Redirect to home page after a short delay
          setTimeout(() => {
            router.push('/');
          }, 2000);
        } else {
          setStatus('error');
          setErrorMessage(data.message || 'Google login failed');
        }
      } catch (error) {
        console.error('Google OAuth callback error:', error);
        setStatus('error');
        setErrorMessage('An unexpected error occurred during login');
      }
    };

    handleCallback();
  }, [searchParams, router, updateUser]);

  if (status === 'loading') {
    return (
      <AuthLayout title="Google Login in Progress..." subtitle="Completing your login">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-sm text-gray-600">
            Completing Google login, please wait...
          </p>
        </div>
      </AuthLayout>
    );
  }

  if (status === 'success') {
    return (
      <AuthLayout title="Login Successful" subtitle="Welcome to Newzora">
        <div className="text-center space-y-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              You have successfully logged in with Google!
            </p>
            <p className="text-sm text-gray-600">
              Redirecting to homepage...
            </p>
          </div>
        </div>
      </AuthLayout>
    );
  }

  // Error state
  return (
    <AuthLayout
      title="Google Login Failed"
      subtitle="An issue occurred during login"
      showBackButton
      backHref="/login"
    >
      <div className="text-center space-y-4">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {errorMessage || 'Google login failed, please try again.'}
          </p>
        </div>

        <div className="space-y-3 pt-4">
          <button
            onClick={() => router.push('/login')}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Again
          </button>
          <button
            onClick={() => router.push('/')}
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back to Home
          </button>
        </div>
      </div>
    </AuthLayout>
  );
}
