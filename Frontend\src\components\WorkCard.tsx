'use client';

import { useRouter } from 'next/navigation';
import { Work, Article, Video, Audio } from '@/types';
import { Card } from '@/components/ui/Card';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AuthPrompt, { useAuthPrompt } from '@/components/AuthPrompt';
import { 
  PlayIcon, 
  PauseIcon, 
  HeartIcon, 
  BookmarkIcon, 
  ShareIcon,
  ChatBubbleLeftIcon,
  EyeIcon,
  ClockIcon,
  UserPlusIcon
} from '@heroicons/react/24/outline';
import { 
  HeartIcon as HeartSolidIcon, 
  BookmarkIcon as BookmarkSolidIcon,
  UserPlusIcon as UserPlusSolidIcon
} from '@heroicons/react/24/solid';

interface WorkCardProps {
  work: Work;
  layout?: 'horizontal' | 'vertical' | 'grid';
  variant?: 'default' | 'featured' | 'minimal';
  showImage?: boolean;
  showAuthor?: boolean;
  showStats?: boolean;
  showInteractions?: boolean;
}

export default function WorkCard({
  work,
  layout = 'horizontal',
  variant = 'default',
  showImage = true,
  showAuthor = true,
  showStats = true,
  showInteractions = true
}: WorkCardProps) {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const { isOpen, action, showPrompt, hidePrompt } = useAuthPrompt();
  const [imageError, setImageError] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleClick = () => {
    const basePath = work.type === 'article' ? '/article' : work.type === 'video' ? '/video' : '/audio';
    router.push(`${basePath}/${work.id}`);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleInteraction = (e: React.MouseEvent, actionType: string) => {
    e.stopPropagation();

    if (!isAuthenticated) {
      showPrompt(actionType);
      return;
    }

    switch (actionType) {
      case 'like':
        setIsLiked(!isLiked);
        // TODO: 调用API更新点赞状态
        break;
      case 'bookmark':
        setIsBookmarked(!isBookmarked);
        // TODO: 调用API更新收藏状态
        break;
      case 'follow':
        setIsFollowing(!isFollowing);
        // TODO: 调用API更新关注状态
        break;
      case 'play':
        setIsPlaying(!isPlaying);
        // TODO: 处理播放逻辑
        break;
      case 'comment':
        // TODO: 跳转到评论区域
        break;
      case 'share':
        // TODO: 打开分享对话框
        break;
    }
  };

  // 格式化时长
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // 获取媒体URL和缩略图
  const getMediaInfo = () => {
    switch (work.type) {
      case 'article':
        return {
          thumbnailUrl: (work as Article).image,
          mediaUrl: null,
          duration: (work as Article).readTime * 60 // 转换为秒
        };
      case 'video':
        return {
          thumbnailUrl: (work as Video).thumbnailUrl,
          mediaUrl: (work as Video).videoUrl,
          duration: (work as Video).duration
        };
      case 'audio':
        return {
          thumbnailUrl: (work as Audio).coverUrl,
          mediaUrl: (work as Audio).audioUrl,
          duration: (work as Audio).duration
        };
      default:
        return { thumbnailUrl: null, mediaUrl: null, duration: 0 };
    }
  };

  const { thumbnailUrl, mediaUrl, duration } = getMediaInfo();

  // 获取作品类型图标
  const getTypeIcon = () => {
    switch (work.type) {
      case 'video':
        return '🎥';
      case 'audio':
        return '🎵';
      default:
        return '📄';
    }
  };

  // 获取分辨率标签
  const getResolutionLabel = () => {
    if (work.type === 'video') {
      return (work as Video).resolution.quality;
    }
    return null;
  };

  if (layout === 'vertical') {
    return (
      <Card
        variant="glass"
        hover="lift"
        padding="none"
        radius="2xl"
        onClick={handleClick}
        className="cursor-pointer group overflow-hidden animate-fade-in"
      >
        {/* 媒体区域 */}
        {showImage && (
          <div className="relative h-48 overflow-hidden">
            <img
              src={imageError ? `https://ui-avatars.com/api/?name=${encodeURIComponent(work.title)}&background=667eea&color=fff&size=400x200` : thumbnailUrl || ''}
              alt={work.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              onError={handleImageError}
            />
            
            {/* 播放按钮覆盖层 */}
            {(work.type === 'video' || work.type === 'audio') && (
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button
                  onClick={(e) => handleInteraction(e, 'play')}
                  className="bg-white/90 rounded-full p-3 hover:bg-white transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="w-6 h-6 text-gray-800" />
                  ) : (
                    <PlayIcon className="w-6 h-6 text-gray-800" />
                  )}
                </button>
              </div>
            )}

            {/* 类型和时长标签 */}
            <div className="absolute top-3 left-3 flex gap-2">
              <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                {getTypeIcon()} {work.type}
              </span>
              {getResolutionLabel() && (
                <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                  {getResolutionLabel()}
                </span>
              )}
            </div>

            {duration > 0 && (
              <div className="absolute bottom-3 right-3">
                <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                  {work.type === 'article' ? `${Math.ceil(duration / 60)} min read` : formatDuration(duration)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* 内容区域 */}
        <div className="p-4">
          {/* 分类 */}
          <div className="mb-2">
            <span className="text-sm text-blue-600 font-medium">
              {work.category}
            </span>
          </div>

          {/* 标题 */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {work.title}
          </h3>

          {/* 描述 */}
          {work.description && (
            <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 mb-3">
              {work.description}
            </p>
          )}

          {/* 作者信息 */}
          {showAuthor && typeof work.author === 'object' && (
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <img
                  src={work.author.avatar}
                  alt={work.author.name}
                  className="w-6 h-6 rounded-full"
                />
                <span className="text-sm text-gray-700">{work.author.name}</span>
              </div>
              
              {showInteractions && (
                <button
                  onClick={(e) => handleInteraction(e, 'follow')}
                  className="text-blue-600 hover:text-blue-700 transition-colors"
                >
                  {isFollowing ? (
                    <UserPlusSolidIcon className="w-4 h-4" />
                  ) : (
                    <UserPlusIcon className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>
          )}

          {/* 统计和交互 */}
          {(showStats || showInteractions) && (
            <div className="flex items-center justify-between text-sm text-gray-500">
              {showStats && (
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <EyeIcon className="w-4 h-4" />
                    {formatNumber(work.views)}
                  </span>
                  <span className="flex items-center gap-1">
                    <HeartIcon className="w-4 h-4" />
                    {formatNumber(work.likes)}
                  </span>
                  <span className="flex items-center gap-1">
                    <ChatBubbleLeftIcon className="w-4 h-4" />
                    {formatNumber(work.comments || 0)}
                  </span>
                </div>
              )}

              {showInteractions && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={(e) => handleInteraction(e, 'like')}
                    className="hover:text-red-500 transition-colors"
                  >
                    {isLiked ? (
                      <HeartSolidIcon className="w-4 h-4 text-red-500" />
                    ) : (
                      <HeartIcon className="w-4 h-4" />
                    )}
                  </button>
                  <button
                    onClick={(e) => handleInteraction(e, 'bookmark')}
                    className="hover:text-blue-500 transition-colors"
                  >
                    {isBookmarked ? (
                      <BookmarkSolidIcon className="w-4 h-4 text-blue-500" />
                    ) : (
                      <BookmarkIcon className="w-4 h-4" />
                    )}
                  </button>
                  <button
                    onClick={(e) => handleInteraction(e, 'share')}
                    className="hover:text-green-500 transition-colors"
                  >
                    <ShareIcon className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    );
  }

  // 水平布局 (默认)
  return (
    <div
      onClick={handleClick}
      className="cursor-pointer group bg-white rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
    >
      <div className="flex gap-6">
        {/* 内容 */}
        <div className="flex-1 min-w-0">
          {/* 分类和类型 */}
          <div className="mb-3 flex items-center gap-2">
            <span className="text-sm text-blue-600 font-medium">
              {work.category}
            </span>
            <span className="text-xs text-gray-500">
              {getTypeIcon()} {work.type}
            </span>
            {getResolutionLabel() && (
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                {getResolutionLabel()}
              </span>
            )}
          </div>

          {/* 标题 */}
          <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {work.title}
          </h2>

          {/* 描述 */}
          {work.description && (
            <p className="text-gray-600 text-sm leading-relaxed line-clamp-2 mb-4">
              {work.description}
            </p>
          )}

          {/* 作者和时间信息 */}
          {showAuthor && typeof work.author === 'object' && (
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <img
                  src={work.author.avatar}
                  alt={work.author.name}
                  className="w-8 h-8 rounded-full"
                />
                <div>
                  <span className="text-sm font-medium text-gray-900">{work.author.name}</span>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span>{work.publishedAt}</span>
                    {duration > 0 && (
                      <>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <ClockIcon className="w-3 h-3" />
                          {work.type === 'article' ? `${Math.ceil(duration / 60)} min read` : formatDuration(duration)}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              {showInteractions && (
                <button
                  onClick={(e) => handleInteraction(e, 'follow')}
                  className="text-blue-600 hover:text-blue-700 transition-colors text-sm"
                >
                  {isFollowing ? 'Following' : 'Follow'}
                </button>
              )}
            </div>
          )}

          {/* 统计和交互 */}
          {(showStats || showInteractions) && (
            <div className="flex items-center justify-between">
              {showStats && (
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <EyeIcon className="w-4 h-4" />
                    {formatNumber(work.views)}
                  </span>
                  <span className="flex items-center gap-1">
                    <HeartIcon className="w-4 h-4" />
                    {formatNumber(work.likes)}
                  </span>
                  <span className="flex items-center gap-1">
                    <ChatBubbleLeftIcon className="w-4 h-4" />
                    {formatNumber(work.comments || 0)}
                  </span>
                </div>
              )}

              {showInteractions && (
                <div className="flex items-center gap-3">
                  <button
                    onClick={(e) => handleInteraction(e, 'like')}
                    className="flex items-center gap-1 text-sm text-gray-500 hover:text-red-500 transition-colors"
                  >
                    {isLiked ? (
                      <HeartSolidIcon className="w-4 h-4 text-red-500" />
                    ) : (
                      <HeartIcon className="w-4 h-4" />
                    )}
                    Like
                  </button>
                  <button
                    onClick={(e) => handleInteraction(e, 'bookmark')}
                    className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-500 transition-colors"
                  >
                    {isBookmarked ? (
                      <BookmarkSolidIcon className="w-4 h-4 text-blue-500" />
                    ) : (
                      <BookmarkIcon className="w-4 h-4" />
                    )}
                    Save
                  </button>
                  <button
                    onClick={(e) => handleInteraction(e, 'share')}
                    className="flex items-center gap-1 text-sm text-gray-500 hover:text-green-500 transition-colors"
                  >
                    <ShareIcon className="w-4 h-4" />
                    Share
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 媒体预览 */}
        {showImage && (
          <div className="w-64 h-40 flex-shrink-0">
            <div className="relative w-full h-full rounded-lg overflow-hidden bg-gray-200">
              <img
                src={imageError ? `https://ui-avatars.com/api/?name=${encodeURIComponent(work.title)}&background=667eea&color=fff&size=400x200` : thumbnailUrl || ''}
                alt={work.title}
                className="w-full h-full object-cover"
                onError={handleImageError}
              />
              
              {/* 播放按钮覆盖层 */}
              {(work.type === 'video' || work.type === 'audio') && (
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button
                    onClick={(e) => handleInteraction(e, 'play')}
                    className="bg-white/90 rounded-full p-2 hover:bg-white transition-colors"
                  >
                    {isPlaying ? (
                      <PauseIcon className="w-5 h-5 text-gray-800" />
                    ) : (
                      <PlayIcon className="w-5 h-5 text-gray-800" />
                    )}
                  </button>
                </div>
              )}

              {/* 时长标签 */}
              {duration > 0 && (
                <div className="absolute bottom-2 right-2">
                  <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {work.type === 'article' ? `${Math.ceil(duration / 60)} min` : formatDuration(duration)}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 认证提示弹窗 */}
      <AuthPrompt
        isOpen={isOpen}
        onClose={hidePrompt}
        action={action}
      />
    </div>
  );
}
