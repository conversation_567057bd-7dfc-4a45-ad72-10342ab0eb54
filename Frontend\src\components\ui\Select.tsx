'use client';

import React, { forwardRef, useState, useRef, useEffect, ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
}

export interface SelectProps {
  label?: string;
  error?: string;
  helperText?: string;
  placeholder?: string;
  variant?: 'default' | 'filled' | 'outlined' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  options: SelectOption[];
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  searchable?: boolean;
  clearable?: boolean;
  multiple?: boolean;
}

const Select = forwardRef<HTMLDivElement, SelectProps>(
  ({
    label,
    error,
    helperText,
    placeholder = 'Select an option...',
    variant = 'default',
    size = 'md',
    fullWidth = false,
    disabled = false,
    options,
    value,
    defaultValue,
    onChange,
    onBlur,
    searchable = false,
    clearable = false,
    multiple = false,
    ...props
  }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(value || defaultValue || '');
    const [searchQuery, setSearchQuery] = useState('');
    const [isFocused, setIsFocused] = useState(false);
    
    const selectRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // 过滤选项
    const filteredOptions = searchable && searchQuery
      ? options.filter(option => 
          option.label.toLowerCase().includes(searchQuery.toLowerCase())
        )
      : options;

    // 获取选中的选项
    const selectedOption = options.find(option => option.value === selectedValue);

    // 处理选择
    const handleSelect = (optionValue: string) => {
      setSelectedValue(optionValue);
      onChange?.(optionValue);
      setIsOpen(false);
      setSearchQuery('');
    };

    // 处理清除
    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedValue('');
      onChange?.('');
    };

    // 点击外部关闭
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
          setIsFocused(false);
          onBlur?.();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [onBlur]);

    // 基础样式
    const baseStyles = [
      'w-full transition-all duration-300',
      'focus-within:ring-2 focus-within:ring-offset-1',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      fullWidth && 'w-full'
    ];

    // 变体样式
    const variantStyles = {
      default: [
        'bg-surface border-2 border-border',
        'focus-within:border-primary focus-within:ring-primary/20',
        error ? 'border-error focus-within:border-error focus-within:ring-error/20' : '',
        'hover:border-primary/50'
      ],
      filled: [
        'bg-surface-2 border-0',
        'focus-within:bg-surface focus-within:ring-primary/20',
        error ? 'focus-within:ring-error/20' : '',
        'hover:bg-surface'
      ],
      outlined: [
        'bg-transparent border-2 border-border',
        'focus-within:border-primary focus-within:ring-primary/20',
        error ? 'border-error focus-within:border-error focus-within:ring-error/20' : '',
        'hover:border-primary/50'
      ],
      glass: [
        'bg-white/10 backdrop-blur-md border border-white/20',
        'focus-within:bg-white/20 focus-within:ring-primary/20',
        error ? 'border-error/50 focus-within:border-error focus-within:ring-error/20' : '',
        'hover:bg-white/15'
      ]
    };

    // 尺寸样式
    const sizeStyles = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg'
    };

    const selectStyles = cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      'rounded-xl cursor-pointer relative',
      disabled && 'cursor-not-allowed'
    );

    const labelStyles = cn(
      'absolute left-4 transition-all duration-300 pointer-events-none',
      'text-text-secondary',
      (isFocused || selectedValue || isOpen)
        ? 'top-2 text-xs font-medium'
        : size === 'sm'
        ? 'top-2 text-sm'
        : size === 'md'
        ? 'top-3 text-base'
        : 'top-4 text-lg',
      (isFocused || isOpen) && !error && 'text-primary',
      error && 'text-error'
    );

    return (
      <div className={cn('relative', fullWidth && 'w-full')} ref={ref}>
        <div
          ref={selectRef}
          className={selectStyles}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          {/* 浮动标签 */}
          {label && (
            <label className={labelStyles}>
              {label}
            </label>
          )}

          {/* 选择器内容 */}
          <div className={cn(
            'flex items-center justify-between',
            label && 'pt-4 pb-1'
          )}>
            <div className="flex-1">
              {searchable && isOpen ? (
                <input
                  ref={inputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-transparent outline-none text-text-primary"
                  placeholder="Search..."
                  autoFocus
                />
              ) : (
                <span className={cn(
                  selectedOption ? 'text-text-primary' : 'text-text-muted'
                )}>
                  {selectedOption ? (
                    <span className="flex items-center gap-2">
                      {selectedOption.icon}
                      {selectedOption.label}
                    </span>
                  ) : (
                    placeholder
                  )}
                </span>
              )}
            </div>

            {/* 右侧图标 */}
            <div className="flex items-center gap-2">
              {clearable && selectedValue && (
                <button
                  onClick={handleClear}
                  className="text-text-muted hover:text-text-primary transition-colors"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
              
              <svg
                className={cn(
                  'w-5 h-5 text-text-muted transition-transform duration-200',
                  isOpen && 'rotate-180'
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        {/* 下拉选项 */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-surface border border-border rounded-xl shadow-2xl z-50 max-h-60 overflow-y-auto animate-fade-in">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                  disabled={option.disabled}
                  className={cn(
                    'w-full flex items-center gap-3 px-4 py-3 text-left transition-colors',
                    'hover:bg-surface-2 focus:bg-surface-2 focus:outline-none',
                    option.disabled && 'opacity-50 cursor-not-allowed',
                    selectedValue === option.value && 'bg-primary/10 text-primary'
                  )}
                >
                  {option.icon}
                  <span>{option.label}</span>
                  {selectedValue === option.value && (
                    <svg className="w-4 h-4 ml-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-text-muted text-center">
                No options found
              </div>
            )}
          </div>
        )}

        {/* 错误信息或帮助文本 */}
        {(error || helperText) && (
          <div className="mt-2 px-1">
            {error ? (
              <p className="text-sm text-error animate-fade-in">{error}</p>
            ) : (
              <p className="text-sm text-text-muted">{helperText}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Select.displayName = 'Select';

export { Select };
