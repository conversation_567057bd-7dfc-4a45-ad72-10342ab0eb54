'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  userId?: number;
}

// 通知状态
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
}

// 通知操作
interface NotificationActions {
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  clearNotification: (id: string) => void;
  clearAllNotifications: () => void;
  fetchNotifications: () => Promise<void>;
}

// Context类型
type NotificationContextType = NotificationState & NotificationActions;

// 创建Context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// NotificationProvider组件
export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(true); // 简化版本，默认连接
  const [isLoading, setIsLoading] = useState(false);

  // 计算未读数量
  const unreadCount = notifications.filter(n => !n.read).length;

  // 添加通知
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      timestamp: new Date(),
      read: false,
    };
    
    setNotifications(prev => [newNotification, ...prev]);
  };

  // 标记为已读
  const markAsRead = async (id: string) => {
    try {
      // 调用API标记为已读
      await fetch(`http://localhost:5000/api/notifications/${id}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }

    // 更新本地状态
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // 标记全部为已读
  const markAllAsRead = async () => {
    try {
      // 调用API标记所有为已读
      await fetch('http://localhost:5000/api/notifications/mark-all-read', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }

    // 更新本地状态
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // 移除通知
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // 清除所有通知
  const clearAll = () => {
    setNotifications([]);
  };

  // 别名方法以兼容notifications页面
  const clearNotification = removeNotification;
  const clearAllNotifications = clearAll;

  // 获取通知（从API）
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('http://localhost:5000/api/notifications');

      if (response.ok) {
        const data = await response.json();
        // 转换API数据格式
        const apiNotifications: Notification[] = data.notifications?.map((notif: any) => ({
          id: notif.id.toString(),
          type: notif.type === 'comment' ? 'info' :
                notif.type === 'like' ? 'success' :
                notif.type === 'system' ? 'warning' : 'info',
          title: notif.title || 'Notification',
          message: notif.content || notif.message,
          timestamp: new Date(notif.createdAt),
          read: notif.isRead || false,
          actionUrl: notif.actionUrl,
          user: notif.user
        })) || [];

        setNotifications(apiNotifications);
        setIsConnected(true);
      } else {
        setIsConnected(false);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // 从API获取通知数据
  React.useEffect(() => {
    const loadNotifications = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('http://localhost:5000/api/notifications');

        if (response.ok) {
          const data = await response.json();
          // 转换API数据格式
          const apiNotifications: Notification[] = data.notifications?.map((notif: any) => ({
            id: notif.id.toString(),
            type: notif.type === 'comment' ? 'info' :
                  notif.type === 'like' ? 'success' :
                  notif.type === 'system' ? 'warning' : 'info',
            title: notif.title || 'Notification',
            message: notif.content || notif.message,
            timestamp: new Date(notif.createdAt),
            read: notif.isRead || false,
            actionUrl: notif.actionUrl,
            user: notif.user
          })) || [];

          setNotifications(apiNotifications);
          setIsConnected(true);
        } else {
          // 降级到示例数据
          console.log('API failed, using sample data');
          const sampleNotifications: Notification[] = [
            {
              id: '1',
              type: 'info',
              title: 'Welcome to Newzora',
              message: 'Thank you for using our news platform!',
              timestamp: new Date(Date.now() - 1000 * 60 * 30),
              read: false,
            },
            {
              id: '2',
              type: 'success',
              title: 'Login Successful',
              message: 'You have successfully logged into the system',
              timestamp: new Date(Date.now() - 1000 * 60 * 10),
              read: false,
            },
          ];
          setNotifications(sampleNotifications);
          setIsConnected(false);
        }
      } catch (error) {
        console.error('Error loading notifications:', error);
        setIsConnected(false);
        // 使用示例数据作为降级方案
        const sampleNotifications: Notification[] = [
          {
            id: '1',
            type: 'info',
            title: 'Welcome to Newzora',
            message: 'Thank you for using our news platform!',
            timestamp: new Date(Date.now() - 1000 * 60 * 30),
            read: false,
          },
        ];
        setNotifications(sampleNotifications);
      } finally {
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, []);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    clearNotification,
    clearAllNotifications,
    fetchNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

// useNotifications hook
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

// 通知权限Hook
export function useNotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission>('default');

  const requestPermission = async () => {
    if ('Notification' in window) {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result;
    }
    return 'denied';
  };

  const checkPermission = () => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
      return Notification.permission;
    }
    return 'denied';
  };

  return {
    permission,
    requestPermission,
    checkPermission,
    isSupported: 'Notification' in window
  };
}
