'use client';

import { useState, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  CloudArrowUpIcon,
  PhotoIcon,
  VideoCameraIcon,
  MicrophoneIcon,
  DocumentTextIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface UploadFile {
  file: File;
  id: string;
  preview?: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  result?: any;
}

export default function MediaUploadPage() {
  const { user, token } = useAuth();
  const router = useRouter();
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  if (!user || !token) {
    router.push('/auth/login');
    return null;
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <PhotoIcon className="w-8 h-8 text-green-600" />;
    if (fileType.startsWith('video/')) return <VideoCameraIcon className="w-8 h-8 text-blue-600" />;
    if (fileType.startsWith('audio/')) return <MicrophoneIcon className="w-8 h-8 text-purple-600" />;
    return <DocumentTextIcon className="w-8 h-8 text-gray-600" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isValidFileType = (file: File) => {
    const validTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/webm', 'video/quicktime',
      'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/mpeg'
    ];
    return validTypes.includes(file.type);
  };

  const isValidFileSize = (file: File) => {
    const maxSize = file.type.startsWith('video/') ? 100 * 1024 * 1024 : // 100MB for videos
                   file.type.startsWith('audio/') ? 50 * 1024 * 1024 :   // 50MB for audio
                   10 * 1024 * 1024; // 10MB for images
    return file.size <= maxSize;
  };

  const createFilePreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  const handleFiles = async (files: FileList) => {
    const newFiles: UploadFile[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!isValidFileType(file)) {
        alert(`File type not supported: ${file.name}`);
        continue;
      }
      
      if (!isValidFileSize(file)) {
        alert(`File too large: ${file.name}`);
        continue;
      }

      const preview = await createFilePreview(file);
      
      newFiles.push({
        file,
        id: Math.random().toString(36).substr(2, 9),
        preview,
        status: 'pending',
        progress: 0
      });
    }

    setUploadFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  };

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id));
  };

  const uploadFile = async (uploadFile: UploadFile) => {
    const formData = new FormData();
    formData.append('file', uploadFile.file);
    formData.append('folder', 'uploads');

    try {
      setUploadFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'uploading', progress: 0 }
          : f
      ));

      const xhr = new XMLHttpRequest();
      
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100);
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, progress }
              : f
          ));
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 201) {
          const result = JSON.parse(xhr.responseText);
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'success', progress: 100, result: result.data.mediaFile }
              : f
          ));
        } else {
          const error = JSON.parse(xhr.responseText);
          setUploadFiles(prev => prev.map(f => 
            f.id === uploadFile.id 
              ? { ...f, status: 'error', error: error.message || 'Upload failed' }
              : f
          ));
        }
      });

      xhr.addEventListener('error', () => {
        setUploadFiles(prev => prev.map(f => 
          f.id === uploadFile.id 
            ? { ...f, status: 'error', error: 'Network error' }
            : f
        ));
      });

      xhr.open('POST', 'http://localhost:5000/api/media/upload');
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.send(formData);

    } catch (error) {
      setUploadFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'error', error: 'Upload failed' }
          : f
      ));
    }
  };

  const uploadAllFiles = () => {
    uploadFiles
      .filter(file => file.status === 'pending')
      .forEach(uploadFile);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <CloudArrowUpIcon className="w-5 h-5 text-blue-600 animate-pulse" />;
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              href="/content"
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-6 h-6" />
            </Link>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Upload Media</h1>
              <p className="text-gray-600">Upload images, videos, and audio files</p>
            </div>
          </div>

          {uploadFiles.length > 0 && (
            <button
              onClick={uploadAllFiles}
              disabled={uploadFiles.every(f => f.status !== 'pending')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Upload All ({uploadFiles.filter(f => f.status === 'pending').length})
            </button>
          )}
        </div>

        {/* Upload Area */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div
            className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
              dragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <CloudArrowUpIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Drop files here or click to browse
            </h3>
            <p className="text-gray-500 mb-6">
              Supports: Images (JPEG, PNG, GIF, WebP), Videos (MP4, WebM, MOV), Audio (MP3, WAV, OGG)
            </p>
            <p className="text-sm text-gray-400 mb-6">
              Max size: 10MB for images, 50MB for audio, 100MB for videos
            </p>
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Choose Files
            </button>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*,video/*,audio/*"
              onChange={handleFileInput}
              className="hidden"
            />
          </div>
        </div>

        {/* File List */}
        {uploadFiles.length > 0 && (
          <div className="bg-white rounded-lg shadow">
            <div className="p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Files ({uploadFiles.length})
              </h2>
              
              <div className="space-y-4">
                {uploadFiles.map((fileItem) => (
                  <div key={fileItem.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start space-x-4">
                      {/* File Preview/Icon */}
                      <div className="flex-shrink-0">
                        {fileItem.preview ? (
                          <img
                            src={fileItem.preview}
                            alt={fileItem.file.name}
                            className="w-16 h-16 object-cover rounded-lg"
                          />
                        ) : (
                          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                            {getFileIcon(fileItem.file.type)}
                          </div>
                        )}
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {fileItem.file.name}
                          </h4>

                          <div className="flex items-center space-x-2">
                            {getStatusIcon(fileItem.status)}

                            <button
                              onClick={() => removeFile(fileItem.id)}
                              className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </div>

                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span>{formatFileSize(fileItem.file.size)}</span>
                          <span className="capitalize">{fileItem.file.type.split('/')[0]}</span>
                          <span className="capitalize">{fileItem.status}</span>
                        </div>

                        {/* Progress Bar */}
                        {fileItem.status === 'uploading' && (
                          <div className="mt-2">
                            <div className="bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${fileItem.progress}%` }}
                              />
                            </div>
                            <div className="mt-1 text-xs text-gray-500">
                              {fileItem.progress}% uploaded
                            </div>
                          </div>
                        )}

                        {/* Error Message */}
                        {fileItem.status === 'error' && fileItem.error && (
                          <div className="mt-2 text-sm text-red-600">
                            {fileItem.error}
                          </div>
                        )}

                        {/* Success Message */}
                        {fileItem.status === 'success' && (
                          <div className="mt-2 text-sm text-green-600">
                            Upload successful!
                          </div>
                        )}

                        {/* Upload Button for Individual Files */}
                        {fileItem.status === 'pending' && (
                          <button
                            onClick={() => uploadFile(fileItem)}
                            className="mt-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                          >
                            Upload
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
