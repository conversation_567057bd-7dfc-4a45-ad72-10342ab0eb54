'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: 'user' | 'admin' | 'moderator';
  redirectTo?: string;
}

export default function ProtectedRoute({
  children,
  requireAuth = true,
  requireRole,
  redirectTo = '/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // 等待认证状态加载完成
    if (isLoading) return;

    // If authentication is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      console.log('🚨 ProtectedRoute: 未认证，跳转到登录页');
      router.push(redirectTo);
      return;
    }

    // If specific role is required
    if (requireRole && user) {
      const hasRequiredRole = checkUserRole(user.role, requireRole);
      if (!hasRequiredRole) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [isAuthenticated, isLoading, user, requireAuth, requireRole, router, redirectTo]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // If specific role is required but user doesn't have it, don't render children
  if (requireRole && user && !checkUserRole(user.role, requireRole)) {
    return null;
  }

  return <>{children}</>;
}

// Helper function to check if user has required role
function checkUserRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'user': 1,
    'moderator': 2,
    'admin': 3
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

// Component for routes that should only be accessible to non-authenticated users
export function GuestRoute({ children, redirectTo = '/' }: { children: React.ReactNode; redirectTo?: string }) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    console.log('🔍 GuestRoute状态检查:', {
      isLoading,
      isAuthenticated,
      hasRedirected,
      redirectTo,
      currentPath: window.location.pathname
    });

    // 增强型认证状态检查，避免循环重定向
    if (!isLoading && isAuthenticated && !hasRedirected) {
      // 检查当前路径，如果刚从重定向而来，避免再次重定向
      const currentPath = window.location.pathname;
      const comingFromRedirect = sessionStorage.getItem('redirect_source');

      // 检查token有效性（简单检查）
      const token = localStorage.getItem('auth_token');
      const user = localStorage.getItem('auth_user');
      const tokenValid = !!(token && user);

      console.log('🔄 GuestRoute: 增强认证检查:', { 
        tokenValid, 
        currentPath,
        comingFromRedirect
      });

      // 只有在token有效且不是循环重定向的情况下才进行跳转
      if (tokenValid && (comingFromRedirect !== redirectTo)) {
        console.log('🔄 GuestRoute: 认证有效，执行重定向到:', redirectTo);
        setHasRedirected(true);

        // 记录此次重定向来源，防止循环
        sessionStorage.setItem('redirect_source', currentPath);

        // 添加短暂延迟确保状态稳定
        setTimeout(() => {
          console.log('🔄 GuestRoute: 执行跳转到:', redirectTo);
          router.replace(redirectTo);

          // 稍后清除重定向记录
          setTimeout(() => {
            sessionStorage.removeItem('redirect_source');
          }, 1000);
        }, 50);
      } else if (!tokenValid) {
        console.warn('⚠️ GuestRoute: 检测到无效认证状态，但isAuthenticated为true');
      } else {
        console.warn('⚠️ GuestRoute: 检测到可能的循环重定向，已阻止');
      }
    }
  }, [isAuthenticated, isLoading, router, redirectTo, hasRedirected]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // 如果用户已认证，显示跳转状态
  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  // 用户未认证，渲染内容
  return <>{children}</>;
}


