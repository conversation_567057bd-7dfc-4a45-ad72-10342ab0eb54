'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRole?: 'user' | 'admin' | 'moderator';
  redirectTo?: string;
}

export default function ProtectedRoute({
  children,
  requireAuth = true,
  requireRole,
  redirectTo = '/login'
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // 等待认证状态加载完成
    if (isLoading) return;

    // If authentication is required but user is not authenticated
    if (requireAuth && !isAuthenticated) {
      console.log('🚨 ProtectedRoute: 未认证，跳转到登录页');
      router.push(redirectTo);
      return;
    }

    // If specific role is required
    if (requireRole && user) {
      const hasRequiredRole = checkUserRole(user.role, requireRole);
      if (!hasRequiredRole) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [isAuthenticated, isLoading, user, requireAuth, requireRole, router, redirectTo]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // If specific role is required but user doesn't have it, don't render children
  if (requireRole && user && !checkUserRole(user.role, requireRole)) {
    return null;
  }

  return <>{children}</>;
}

// Helper function to check if user has required role
function checkUserRole(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'user': 1,
    'moderator': 2,
    'admin': 3
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

  return userLevel >= requiredLevel;
}

// Component for routes that should only be accessible to non-authenticated users
export function GuestRoute({ children, redirectTo = '/' }: { children: React.ReactNode; redirectTo?: string }) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    // 等待认证状态加载完成，并确保用户数据也已加载
    if (!isLoading && isAuthenticated && user && !hasRedirected) {
      // 检查当前路径，避免在目标路径上进行跳转
      const currentPath = window.location.pathname;

      // 只在特定的访客页面（登录、注册等）进行跳转
      const guestOnlyPaths = ['/auth/login', '/auth/register', '/login', '/register', '/auth/supabase-login', '/auth/supabase-register'];
      const isGuestOnlyPath = guestOnlyPaths.some(path => currentPath.startsWith(path));

      if (isGuestOnlyPath && currentPath !== redirectTo) {
        console.log('🔄 GuestRoute: User is authenticated, redirecting from', currentPath, 'to:', redirectTo);
        setHasRedirected(true);

        // 添加短暂延迟避免竞态条件
        setTimeout(() => {
          router.replace(redirectTo);
        }, 100);
      }
    }
  }, [isAuthenticated, isLoading, user, router, redirectTo, hasRedirected]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // 检查是否在访客专用页面
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
  const guestOnlyPaths = ['/auth/login', '/auth/register', '/login', '/register', '/auth/supabase-login', '/auth/supabase-register'];
  const isGuestOnlyPath = guestOnlyPaths.some(path => currentPath.startsWith(path));

  // 如果用户已认证且在访客专用页面，显示跳转状态
  if (!isLoading && isAuthenticated && user && isGuestOnlyPath) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting...</p>
        </div>
      </div>
    );
  }

  // 用户未认证或不在访客专用页面，渲染内容
  return <>{children}</>;
}


