'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { socketService } from '@/services/socketService';

export interface Notification {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'system' | 'article_published' | 'article_approved' | 'security';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  user?: {
    name: string;
    avatar: string;
  };
  data?: {
    articleId?: string;
    articleTitle?: string;
    commentId?: string;
    userId?: string;
  };
}

export function useNotifications() {
  const { user, isAuthenticated, token } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 获取通知列表
  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated || !token) return;

    try {
      setIsLoading(true);
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const transformedNotifications: Notification[] = data.notifications?.map((notif: any) => ({
          id: notif.id.toString(),
          type: notif.type,
          title: notif.title,
          message: notif.content,
          timestamp: new Date(notif.createdAt),
          read: notif.isRead,
          actionUrl: notif.actionUrl,
          user: notif.user ? {
            name: notif.user.display_name || notif.user.username,
            avatar: notif.user.avatar_url || '/default-avatar.png'
          } : undefined,
          data: notif.data
        })) || [];

        setNotifications(transformedNotifications);
        setUnreadCount(transformedNotifications.filter(n => !n.read).length);
      }
    } catch (error) {
      console.error('❌ Failed to fetch notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, token]);

  // 标记单个通知为已读
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, read: true }
              : notif
          )
        );
        
        setUnreadCount(prev => Math.max(0, prev - 1));
        
        // 通过Socket通知服务器
        if (socketService.isSocketConnected()) {
          socketService.markNotificationAsRead(parseInt(notificationId));
        }
      }
    } catch (error) {
      console.error('❌ Failed to mark notification as read:', error);
    }
  }, [isAuthenticated, token]);

  // 标记所有通知为已读
  const markAllAsRead = useCallback(async () => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/notifications/read-all`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, read: true }))
        );
        
        setUnreadCount(0);
        
        // 通过Socket通知服务器
        if (socketService.isSocketConnected()) {
          socketService.markAllNotificationsAsRead();
        }
      }
    } catch (error) {
      console.error('❌ Failed to mark all notifications as read:', error);
    }
  }, [isAuthenticated, token]);

  // 初始化Socket连接和事件监听
  useEffect(() => {
    if (!isAuthenticated || !token) {
      setIsConnected(false);
      return;
    }

    const initializeSocket = async () => {
      try {
        // 连接Socket
        const connected = await socketService.connect(token);
        setIsConnected(connected);

        if (connected) {
          // 监听新通知
          socketService.on('new_notification', (notification: any) => {
            console.log('🔔 收到新通知:', notification);
            
            const newNotification: Notification = {
              id: notification.id.toString(),
              type: notification.type,
              title: notification.title,
              message: notification.content,
              timestamp: new Date(notification.createdAt),
              read: false,
              actionUrl: notification.actionUrl,
              user: notification.user ? {
                name: notification.user.display_name || notification.user.username,
                avatar: notification.user.avatar_url || '/default-avatar.png'
              } : undefined,
              data: notification.data
            };

            setNotifications(prev => [newNotification, ...prev]);
            setUnreadCount(prev => prev + 1);
          });

          // 监听未读数量更新
          socketService.on('unread_count_updated', (data: { count: number }) => {
            console.log('🔢 未读数量更新:', data.count);
            setUnreadCount(data.count);
          });

          // 监听连接状态变化
          socketService.on('connect', () => {
            console.log('🔌 Socket连接成功');
            setIsConnected(true);
          });

          socketService.on('disconnect', () => {
            console.log('🔌 Socket连接断开');
            setIsConnected(false);
          });

          socketService.on('connect_error', (error: any) => {
            console.error('❌ Socket连接错误:', error);
            setIsConnected(false);
          });
        }
      } catch (error) {
        console.error('❌ Socket初始化失败:', error);
        setIsConnected(false);
      }
    };

    initializeSocket();

    // 清理函数
    return () => {
      socketService.disconnect();
      setIsConnected(false);
    };
  }, [isAuthenticated, token]);

  // 获取初始通知数据
  useEffect(() => {
    if (isAuthenticated) {
      fetchNotifications();
    } else {
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [isAuthenticated, fetchNotifications]);

  return {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    markAsRead,
    markAllAsRead,
    fetchNotifications
  };
}
