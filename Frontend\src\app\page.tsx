'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import CategorySidebar from '@/components/CategorySidebar';
import WorkCard from '@/components/WorkCard';
import { Work } from '@/types';
import { mockWorks, mockWorksByCategory, trendingWorks, latestWorks } from '@/data/mockWorks';
import { useAuth } from '@/contexts/AuthContext';

export default function Home() {
  const [works, setWorks] = useState<Work[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('trending');
  const { isAuthenticated, refreshAuthState } = useAuth();

  // 页面加载时强制刷新认证状态
  useEffect(() => {
    console.log('🏠 主页加载，刷新认证状态');
    refreshAuthState();
  }, [refreshAuthState]);

  // 监听认证状态变化事件
  useEffect(() => {
    const handleAuthStateChange = () => {
      console.log('🏠 主页收到认证状态变化事件，强制刷新');
      refreshAuthState();
    };

    window.addEventListener('auth-state-changed', handleAuthStateChange);
    return () => window.removeEventListener('auth-state-changed', handleAuthStateChange);
  }, [refreshAuthState]);

  useEffect(() => {
    fetchWorks();
  }, [selectedCategory]);

  const fetchWorks = async () => {
    try {
      setLoading(true);

      // 使用模拟数据，根据选择的分类过滤
      let filteredWorks: Work[] = [];

      switch (selectedCategory) {
        case 'trending':
          filteredWorks = trendingWorks.slice(0, 12);
          break;
        case 'all':
          filteredWorks = latestWorks.slice(0, 12);
          break;
        default:
          filteredWorks = mockWorksByCategory[selectedCategory] || [];
          break;
      }

      setWorks(filteredWorks);
    } catch (error) {
      console.error('Error fetching works:', error);
      // 使用默认数据作为后备
      setWorks(mockWorks.slice(0, 12));
    } finally {
      setLoading(false);
    }
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex">
          <CategorySidebar
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
          />
          <main className="flex-1 p-8">
            <div className="space-y-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg p-6 animate-pulse">
                  <div className="flex gap-6">
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div>
                      <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                      <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    </div>
                    <div className="w-64 h-40 bg-gray-200 rounded-lg"></div>
                  </div>
                </div>
              ))}
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex">
        {/* Category Sidebar */}
        <CategorySidebar
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />

        {/* Main Content */}
        <main className="flex-1 p-8">
          {/* Content Header */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              {selectedCategory === 'trending' ? 'Recommended for you' : `${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Works`}
            </h2>
            <p className="text-gray-600 mt-1">
              Discover amazing articles, videos, and audio content from creators around the world
            </p>
          </div>

          {/* Works List */}
          <div className="space-y-6">
            {works.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-6xl mb-4">📝</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No content found</h3>
                <p className="text-gray-600">Try selecting a different category or check back later.</p>
              </div>
            ) : (
              works.map((work) => (
                <WorkCard
                  key={work.id}
                  work={work}
                  layout="horizontal"
                  showImage={true}
                  showAuthor={true}
                  showStats={true}
                  showInteractions={isAuthenticated}
                />
              ))
            )}
          </div>

          {/* Load More Button */}
          {works.length > 0 && (
            <div className="text-center mt-12">
              <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Load More Content
              </button>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
