登录后立即跳转回登录页面问题诊断报告
问题描述
在基于 React 18 + Next.js 14 + JWT 认证的 Web 应用中，用户在登录页面输入正确的用户名和密码后，虽然登录请求成功，但页面会立即跳转回登录页面，导致用户无法正常进入应用的主界面。这是一个典型的认证状态管理和路由保护问题。

技术栈环境
前端: React 18 + Next.js 14 + Tailwind CSS
后端: Node.js + Express.js
数据库: PostgreSQL + Sequelize ORM
认证方式: JWT Token
状态管理: React Context API
问题根因分析
1. 认证状态管理问题
问题文件: frontend/contexts/AuthContext.tsx

具体问题:

JWT Token 存储后未能正确更新认证状态
checkAuth 函数在页面刷新时执行，但可能在登录成功后立即执行验证失败
认证状态的异步更新与路由跳转时机不匹配
逻辑缺陷:

// 问题代码示例
const login = async (email: string, password: string) => {
  // 登录成功后立即设置用户状态
  setUser(data.user);
  // 但是 isAuthenticated 的计算可能存在延迟
  // 导致路由守卫仍然认为用户未认证
};

Copy

Apply

2. Next.js 中间件配置问题
问题文件: frontend/middleware.ts

具体问题:

中间件的路由匹配规则过于严格或宽泛
Token 验证逻辑在中间件层面失效
公开路径和受保护路径的判断逻辑错误
逻辑缺陷:

// 问题代码示例
export function middleware(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  
  // 如果这里的 token 获取失败，会导致已登录用户被重定向到登录页
  if (!token && !publicPaths.includes(pathname)) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
}

Copy

Apply

3. JWT Token 存储和传输问题
涉及文件:

backend/routes/auth.js (后端认证路由)
frontend/contexts/AuthContext.tsx (前端认证上下文)
具体问题:

Token 存储方式不一致（localStorage vs Cookies）
跨域请求时 Cookies 未正确传输
Token 过期时间设置与前端验证不匹配
4. 路由保护逻辑冲突
问题文件:

frontend/app/login/page.tsx
frontend/app/page.tsx
frontend/middleware.ts
具体问题:

多层路由保护导致逻辑冲突
页面级别的认证检查与中间件检查重复执行
认证状态加载期间的路由跳转处理不当
5. 异步状态竞争问题
逻辑问题:

登录成功后的状态更新是异步的
路由跳转可能在状态更新完成前执行
useEffect 依赖项变化导致的重复执行
解决方案详解
1. 认证上下文优化
文件: frontend/contexts/AuthContext.tsx

解决方案:

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean; // 关键：添加加载状态
  isAuthenticated: boolean;
}

// 优化登录函数
const login = async (email: string, password: string): Promise<boolean> => {
  try {
    setLoading(true);
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include', // 关键：包含 cookies
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();
    if (response.ok && data.success) {
      // 双重存储策略
      localStorage.setItem('auth-token', data.token);
      setUser(data.user); // 立即更新用户状态
      return true;
    }
    return false;
  } catch (error) {
    console.error('Login error:', error);
    return false;
  } finally {
    setLoading(false);
  }
};

Copy

Apply

2. 中间件路由保护优化
文件: frontend/middleware.ts

解决方案:

export function middleware(request: NextRequest) {
  // 多种方式获取 token
  const token = request.cookies.get('auth-token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '');
  
  const { pathname } = request.nextUrl;
  const publicPaths = ['/login', '/register', '/api/auth/login', '/api/auth/register'];
  
  // 精确的路径匹配
  if (publicPaths.some(path => pathname.startsWith(path))) {
    // 防止已登录用户访问登录页
    if (token && (pathname === '/login' || pathname === '/register')) {
      return NextResponse.redirect(new URL('/', request.url));
    }
    return NextResponse.next();
  }

  // 未认证用户的处理
  if (!token) {
    if (pathname.startsWith('/api/')) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

Copy

Apply

3. 后端认证接口优化
文件: backend/routes/auth.js

解决方案:

router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // 用户验证逻辑...
    const user = await User.findOne({ where: { email } });
    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (user && isValidPassword) {
      const token = jwt.sign(
        { userId: user.id, email: user.email },
        process.env.JWT_SECRET,
        { expiresIn: '24h' }
      );

      // 双重设置：Cookie + 响应体
      res.cookie('auth-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000
      });

      res.json({
        success: true,
        message: 'Login successful',
        user: { id: user.id, email: user.email, name: user.name },
        token // 同时返回 token 供前端使用
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
});

Copy

Apply

4. 登录页面逻辑优化
文件: frontend/app/login/page.tsx

解决方案:

export default function LoginPage() {
  const { login, isAuthenticated, loading } = useAuth();
  const router = useRouter();

  // 关键：正确处理认证状态检查
  useEffect(() => {
    if (!loading && isAuthenticated) {
      router.push('/');
    }
  }, [isAuthenticated, loading, router]);

  // 加载状态处理
  if (loading) {
    return <LoadingSpinner />;
  }

  // 已认证用户不显示登录表单
  if (isAuthenticated) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await login(email, password);
    if (success) {
      // 登录成功后，useEffect 会处理重定向
      router.push('/');
    } else {
      setError('Invalid credentials');
    }
  };

  return (
    // 登录表单 JSX...
  );
}

Copy

Apply

5. API 路由代理配置
文件: frontend/app/api/auth/[...slug]/route.ts

解决方案:

async function handleRequest(request: NextRequest, slug: string[], method: string) {
  try {
    const url = `${API_BASE_URL}/api/auth/${slug.join('/')}`;
    
    const headers: HeadersInit = { 'Content-Type': 'application/json' };
    
    // 转发认证头
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    const response = await fetch(url, {
      method,
      headers,
      body: method === 'POST' ? await request.text() : undefined,
    });

    const data = await response.json();
    const nextResponse = NextResponse.json(data, { status: response.status });

    // 关键：转发 Set-Cookie 头
    const setCookieHeader = response.headers.get('set-cookie');
    if (setCookieHeader) {
      nextResponse.headers.set('set-cookie', setCookieHeader);
    }

    return nextResponse;
  } catch (error) {
    return NextResponse.json({ success: false, message: 'Internal server error' }, { status: 500 });
  }
}

Copy

Apply

关键修复点总结
1. 状态管理层面
加载状态管理: 添加 loading 状态防止认证检查期间的错误跳转
异步状态同步: 确保登录成功后状态更新的时序正确
双重存储策略: 同时使用 localStorage 和 HTTP-only Cookies
2. 路由保护层面
多层防护: 中间件 + 页面级别 + Context 三重保护
精确路径匹配: 避免路径匹配规则冲突
条件渲染: 根据认证状态条件性渲染组件
3. 网络通信层面
跨域配置: 正确配置 CORS 和 credentials
Token 传输: 多种方式传输和验证 Token
错误处理: 完善的网络请求错误处理
4. 服务器配置层面
JWT 配置: 正确的 Token 生成和验证
Cookie 设置: 安全的 Cookie 配置
中间件顺序: 正确的 Express 中间件执行顺序
测试验证方案
功能测试: 验证登录流程的完整性
状态测试: 检查认证状态在各种场景下的正确性
路由测试: 验证受保护路由的访问控制
网络测试: 检查 Token 传输和验证的正确性
边界测试: 测试 Token 过期、网络异常等边界情况
通过以上全面的问题分析和解决方案实施，可以彻底解决登录后立即跳转回登录页面的问题，确保用户认证流程的稳定性和可靠性。