'use client';

import React, { useState } from 'react';

export default function SimpleAuthTestPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 直接调用后端API注册
  const testRegister = async () => {
    setIsLoading(true);
    setResult('正在注册...');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username || `user${Date.now()}`,
          email: email || `test${Date.now()}@example.com`,
          password: password || 'TestPassword123!'
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`✅ 注册成功！\n${JSON.stringify(data, null, 2)}`);
      } else {
        setResult(`❌ 注册失败！\n状态码: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`);
      }
    } catch (error) {
      setResult(`💥 网络错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 直接调用后端API登录
  const testLogin = async () => {
    setIsLoading(true);
    setResult('正在登录...');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email || '<EMAIL>',
          password: password || 'TestPassword123!'
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(`✅ 登录成功！\n${JSON.stringify(data, null, 2)}`);
      } else {
        setResult(`❌ 登录失败！\n状态码: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`);
      }
    } catch (error) {
      setResult(`💥 网络错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试后端连接
  const testConnection = async () => {
    setIsLoading(true);
    setResult('测试连接...');
    
    try {
      const response = await fetch('http://localhost:5000/api/health');
      const data = await response.json();
      
      if (response.ok) {
        setResult(`✅ 后端连接正常！\n${JSON.stringify(data, null, 2)}`);
      } else {
        setResult(`❌ 后端响应异常！\n状态码: ${response.status}`);
      }
    } catch (error) {
      setResult(`💥 无法连接后端: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 生成随机测试数据
  const generateTestData = () => {
    const timestamp = Date.now();
    setEmail(`test${timestamp}@example.com`);
    setPassword('TestPassword123!');
    setUsername(`user${timestamp}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-red-600">
            简单直接的认证测试 - 绕过所有复杂系统
          </h1>
          
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <h2 className="text-lg font-semibold text-red-800 mb-2">说明</h2>
            <p className="text-red-700">
              这个页面直接调用后端API，绕过前端的所有认证系统和复杂逻辑。
              如果这里能正常工作，说明后端没问题，问题在前端的认证系统配置。
            </p>
          </div>

          {/* 输入表单 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="TestPassword123!"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">用户名</label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="testuser"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button
              onClick={generateTestData}
              disabled={isLoading}
              className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:opacity-50"
            >
              生成测试数据
            </button>
            
            <button
              onClick={testConnection}
              disabled={isLoading}
              className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              测试后端连接
            </button>
            
            <button
              onClick={testRegister}
              disabled={isLoading}
              className="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              直接注册
            </button>
            
            <button
              onClick={testLogin}
              disabled={isLoading}
              className="bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 disabled:opacity-50"
            >
              直接登录
            </button>
          </div>

          {/* 快速测试按钮 */}
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">快速测试</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={async () => {
                  generateTestData();
                  setTimeout(testRegister, 500);
                }}
                disabled={isLoading}
                className="bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 disabled:opacity-50"
              >
                一键注册测试
              </button>
              
              <button
                onClick={async () => {
                  setEmail('<EMAIL>');
                  setPassword('TestPassword123!');
                  setTimeout(testLogin, 500);
                }}
                disabled={isLoading}
                className="bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                测试预置账户登录
              </button>
            </div>
          </div>

          {/* 结果显示 */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm min-h-32">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">测试结果</h3>
              {isLoading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-400"></div>
              )}
            </div>
            
            <pre className="whitespace-pre-wrap break-words">
              {result || '点击上方按钮开始测试...'}
            </pre>
          </div>

          {/* 调试信息 */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">调试信息</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>后端地址:</strong> http://localhost:5000</p>
              <p><strong>注册API:</strong> POST /api/users/register</p>
              <p><strong>登录API:</strong> POST /api/users/login</p>
              <p><strong>健康检查:</strong> GET /api/health</p>
              <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
            </div>
          </div>

          {/* 导航链接 */}
          <div className="mt-6 flex gap-4 justify-center">
            <a href="/test-registration-fix" className="text-blue-600 hover:text-blue-800 underline">
              注册修复测试页面
            </a>
            <a href="/auth/supabase-login" className="text-blue-600 hover:text-blue-800 underline">
              Supabase登录页面
            </a>
            <a href="/" className="text-blue-600 hover:text-blue-800 underline">
              返回首页
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
