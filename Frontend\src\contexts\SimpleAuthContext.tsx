'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  setAuthData: (user: User, token: string) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function SimpleAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!(user && token);

  // 初始化：从localStorage恢复认证状态
  useEffect(() => {
    console.log('🔄 SimpleAuth: Initializing...');
    
    try {
      const storedToken = localStorage.getItem('auth_token');
      const storedUser = localStorage.getItem('auth_user');
      
      if (storedToken && storedUser) {
        const userData = JSON.parse(storedUser);
        setToken(storedToken);
        setUser(userData);
        console.log('✅ SimpleAuth: Restored from localStorage');
      } else {
        console.log('ℹ️ SimpleAuth: No stored auth data');
      }
    } catch (error) {
      console.error('❌ SimpleAuth: Error restoring auth data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const setAuthData = (userData: User, authToken: string) => {
    console.log('🔐 SimpleAuth: Setting auth data');
    
    // 更新状态
    setUser(userData);
    setToken(authToken);
    
    // 存储到localStorage
    localStorage.setItem('auth_token', authToken);
    localStorage.setItem('auth_user', JSON.stringify(userData));
    
    // 设置cookies
    const cookieOptions = `path=/; max-age=${7 * 24 * 60 * 60}; samesite=lax`;
    document.cookie = `auth_token=${authToken}; ${cookieOptions}`;
    document.cookie = `user_role=${userData.role}; ${cookieOptions}`;
    
    console.log('✅ SimpleAuth: Auth data set successfully');
  };

  const login = async (email: string, password: string) => {
    console.log('🔐 SimpleAuth: Login attempt');
    
    try {
      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ identifier: email, password }),
      });

      const data = await response.json();
      
      if (data.success) {
        setAuthData(data.user, data.token);
        return { success: true, message: 'Login successful' };
      } else {
        return { success: false, message: data.message || 'Login failed' };
      }
    } catch (error) {
      console.error('❌ SimpleAuth: Login error:', error);
      return { success: false, message: 'Network error' };
    }
  };

  const logout = () => {
    console.log('🚪 SimpleAuth: Logging out');
    
    // 清除状态
    setUser(null);
    setToken(null);
    
    // 清除存储
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    
    // 清除cookies
    document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    document.cookie = 'user_role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    
    console.log('✅ SimpleAuth: Logged out successfully');
  };

  const value = {
    user,
    token,
    isAuthenticated,
    isLoading,
    login,
    logout,
    setAuthData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useSimpleAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
}
