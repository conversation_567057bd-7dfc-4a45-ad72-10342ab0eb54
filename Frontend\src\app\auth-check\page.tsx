'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthCheckPage() {
  const { user, token, isAuthenticated, loading, error, isHydrated } = useAuth();
  const [clientInfo, setClientInfo] = useState<any>({});

  useEffect(() => {
    setClientInfo({
      localStorage: {
        auth_token: localStorage.getItem('auth_token'),
        auth_user: localStorage.getItem('auth_user'),
        authenticated: localStorage.getItem('authenticated'),
      },
      sessionStorage: {
        auth_verified: sessionStorage.getItem('auth_verified'),
        login_success: sessionStorage.getItem('login_success'),
      },
      cookies: document.cookie,
    });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">认证状态检查</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">AuthContext 状态</h2>
            <div className="space-y-2">
              <p><strong>用户:</strong> {user?.username || 'null'}</p>
              <p><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'null'}</p>
              <p><strong>已认证:</strong> {isAuthenticated ? '是' : '否'}</p>
              <p><strong>加载中:</strong> {loading ? '是' : '否'}</p>
              <p><strong>已水合:</strong> {isHydrated ? '是' : '否'}</p>
              <p><strong>错误:</strong> {error || '无'}</p>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">客户端存储</h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">localStorage:</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
                  {JSON.stringify(clientInfo.localStorage, null, 2)}
                </pre>
              </div>
              <div>
                <h3 className="font-medium">sessionStorage:</h3>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
                  {JSON.stringify(clientInfo.sessionStorage, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
