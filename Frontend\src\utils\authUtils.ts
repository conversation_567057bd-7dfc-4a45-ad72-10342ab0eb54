/**
 * 认证工具函数
 */

// 清除所有认证相关的存储数据
export const clearAllAuthData = () => {
  console.log('🧹 清除所有认证数据');
  
  // 清除 localStorage
  localStorage.removeItem('auth_token');
  localStorage.removeItem('auth_user');
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('authProvider');
  localStorage.removeItem('user');
  
  // 清除所有可能的认证相关 cookies
  const cookiesToClear = [
    'auth_token',
    'user_role',
    'session_id',
    'jwt_token',
    'access_token',
    'refresh_token'
  ];
  
  cookiesToClear.forEach(cookieName => {
    // 清除当前域的 cookie
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    // 清除可能的子域 cookie
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.localhost;`;
    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=localhost;`;
  });
  
  console.log('✅ 所有认证数据已清除');
};

// 检查认证状态一致性
export const checkAuthConsistency = () => {
  const token = localStorage.getItem('auth_token');
  const user = localStorage.getItem('auth_user');
  const cookies = document.cookie;
  
  console.log('🔍 认证状态检查:');
  console.log('- localStorage token:', token ? '存在' : '不存在');
  console.log('- localStorage user:', user ? '存在' : '不存在');
  console.log('- cookies:', cookies || '无');
  
  return {
    hasToken: !!token,
    hasUser: !!user,
    cookies: cookies
  };
};

// 强制刷新认证状态
export const forceAuthRefresh = () => {
  console.log('🔄 强制刷新认证状态');
  
  // 清除所有数据
  clearAllAuthData();
  
  // 刷新页面
  window.location.reload();
};

// 调试认证状态
export const debugAuthState = () => {
  console.log('🐛 认证状态调试信息:');
  
  // localStorage 信息
  console.log('📦 localStorage:');
  console.log('- auth_token:', localStorage.getItem('auth_token'));
  console.log('- auth_user:', localStorage.getItem('auth_user'));
  console.log('- isAuthenticated:', localStorage.getItem('isAuthenticated'));
  console.log('- authProvider:', localStorage.getItem('authProvider'));
  
  // Cookie 信息
  console.log('🍪 Cookies:');
  const cookies = document.cookie.split(';').reduce((acc, cookie) => {
    const [key, value] = cookie.trim().split('=');
    acc[key] = value;
    return acc;
  }, {} as Record<string, string>);
  console.log(cookies);
  
  // 检查一致性
  const consistency = checkAuthConsistency();
  console.log('🔍 一致性检查:', consistency);
  
  return {
    localStorage: {
      auth_token: localStorage.getItem('auth_token'),
      auth_user: localStorage.getItem('auth_user'),
      isAuthenticated: localStorage.getItem('isAuthenticated'),
      authProvider: localStorage.getItem('authProvider')
    },
    cookies,
    consistency
  };
};

// 在开发环境下暴露到全局对象，方便调试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).authUtils = {
    clearAllAuthData,
    checkAuthConsistency,
    forceAuthRefresh,
    debugAuthState
  };
  
  console.log('🛠️ 认证工具已暴露到 window.authUtils');
  console.log('可用方法: clearAllAuthData(), checkAuthConsistency(), forceAuthRefresh(), debugAuthState()');
}
