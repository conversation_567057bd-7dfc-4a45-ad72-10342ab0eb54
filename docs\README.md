# OneNews 文档

欢迎来到 OneNews 项目文档！

## 📁 文档结构

### 部署文档 (`deployment/`)
- [Docker 部署指南](deployment/DOCKER_DEPLOYMENT.md) - 使用 Docker 部署 OneNews
- [Nginx 部署指南](deployment/NGINX_DEPLOYMENT.md) - Nginx 反向代理配置
- [生产环境设置](deployment/PRODUCTION_SETUP.md) - 生产环境完整部署指南

### 设置指南 (`setup/`)
- [邮件服务设置](setup/EMAIL_SERVICE_SETUP.md) - 配置邮件服务
- [OAuth 设置](setup/OAUTH_SETUP.md) - 社交登录配置

## 🚀 快速开始

1. **环境要求**
   - Node.js 18+
   - PostgreSQL 13+
   - Redis (可选，用于缓存)

2. **安装依赖**
   ```bash
   # 后端
   cd Backend
   npm install
   
   # 前端
   cd ../Frontend
   npm install
   ```

3. **启动项目**
   ```bash
   # 使用启动脚本 (推荐)
   ./start.ps1  # Windows PowerShell
   ./start.bat  # Windows CMD
   
   # 或手动启动
   cd Backend && npm run dev
   cd Frontend && npm run dev
   ```

## 📚 更多信息

- [主项目 README](../README.md) - 项目概述和基本信息
- [工具和脚本](../tools/) - 开发和部署工具
- [测试文件](../tests/) - 测试脚本和数据

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目采用 MIT 许可证。
