-- OneNews 数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE onenews'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'onenews');

-- 连接到 onenews 数据库
\c onenews;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建全文搜索配置
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS chinese (COPY = simple);

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建基础索引（Sequelize会自动创建表结构）
-- 这里只是预留一些可能需要的配置

-- 创建用户角色（如果需要）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'onenews_app') THEN
        CREATE ROLE onenews_app WITH LOGIN PASSWORD 'app_password';
    END IF;
END
$$;

-- 授权
GRANT CONNECT ON DATABASE onenews TO onenews_app;
GRANT USAGE ON SCHEMA public TO onenews_app;
GRANT CREATE ON SCHEMA public TO onenews_app;

-- 输出初始化完成信息
SELECT 'OneNews database initialized successfully' as status;
