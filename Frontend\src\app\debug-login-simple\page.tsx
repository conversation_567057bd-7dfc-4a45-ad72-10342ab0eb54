'use client';

import React, { useState } from 'react';

export default function DebugLoginSimple() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('TestPassword123!');
  const [result, setResult] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const testLogin = async () => {
    console.log('🔍 开始测试登录...');
    setIsLoading(true);
    setResult('正在登录...');

    try {
      console.log('📤 发送登录请求到:', 'http://localhost:5000/api/users/login');
      console.log('📤 请求数据:', { identifier: email, password: password });

      const response = await fetch('http://localhost:5000/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          identifier: email,
          password: password
        }),
      });

      console.log('📡 响应状态:', response.status);
      console.log('📡 响应头:', response.headers);

      const data = await response.json();
      console.log('📡 响应数据:', data);

      if (data.success) {
        setResult(`✅ 登录成功！\n用户: ${data.user.username}\nToken: ${data.token.substring(0, 20)}...`);
        
        // 保存到localStorage
        localStorage.setItem('auth_token', data.token);
        localStorage.setItem('auth_user', JSON.stringify(data.user));
        console.log('💾 已保存到localStorage');
      } else {
        setResult(`❌ 登录失败: ${data.message}`);
      }
    } catch (error) {
      console.error('💥 登录错误:', error);
      setResult(`💥 网络错误: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const checkStorage = () => {
    const token = localStorage.getItem('auth_token');
    const user = localStorage.getItem('auth_user');
    
    console.log('🔍 检查localStorage:');
    console.log('Token:', token);
    console.log('User:', user);
    
    setResult(`Token: ${token ? token.substring(0, 20) + '...' : '无'}\nUser: ${user || '无'}`);
  };

  const clearStorage = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    setResult('已清除localStorage');
    console.log('🗑️ 已清除localStorage');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6 text-center">登录调试工具</h1>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">邮箱</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">密码</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div className="space-y-2">
            <button
              onClick={testLogin}
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? '登录中...' : '测试登录'}
            </button>
            
            <button
              onClick={checkStorage}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
            >
              检查存储
            </button>
            
            <button
              onClick={clearStorage}
              className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
            >
              清除存储
            </button>
          </div>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded-md">
              <pre className="text-sm whitespace-pre-wrap">{result}</pre>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
