# OneNews 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# =================================
# 基础配置
# =================================
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# =================================
# 数据库配置 (PostgreSQL)
# =================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=onenews
DB_USER=postgres
DB_PASSWORD=wasd080980!

# =================================
# Redis 配置
# =================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# =================================
# JWT 和会话安全配置
# =================================
JWT_SECRET=your-super-secure-jwt-secret-key-here
SESSION_SECRET=your-super-secure-session-secret-key-here

# =================================
# 邮件服务配置
# =================================
EMAIL_PROVIDER=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=OneNews <<EMAIL>>

# =================================
# OAuth 社交登录配置
# =================================
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# =================================
# Web Push 通知配置
# =================================
VAPID_PUBLIC_KEY=your_vapid_public_key
VAPID_PRIVATE_KEY=your_vapid_private_key

# =================================
# 文件上传配置
# =================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# =================================
# 安全配置
# =================================
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# =================================
# API 限流配置
# =================================
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# =================================
# 开发工具配置
# =================================
# 设置为 true 启用详细日志
DEBUG=false

# 设置为 true 启用 API 文档
ENABLE_DOCS=true

# =================================
# 生产环境额外配置
# =================================
# 仅在生产环境中使用
# SSL_KEY_PATH=/path/to/ssl/private.key
# SSL_CERT_PATH=/path/to/ssl/certificate.crt
# DOMAIN=your-domain.com

# =================================
# 第三方服务配置
# =================================
# AWS S3 (可选，用于文件存储)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=your-bucket-name

# SendGrid (可选，用于邮件服务)
# SENDGRID_API_KEY=your_sendgrid_api_key

# =================================
# 监控和日志配置
# =================================
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# =================================
# 数据库连接池配置
# =================================
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
