'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import AuthLayout from '@/components/AuthLayout';

export default function VerifyEmailPage() {
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading');
  const [errorMessage, setErrorMessage] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [email, setEmail] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading } = useAuth();
  const token = searchParams.get('token');

  // 只在没有验证token且已认证时才跳转
  useEffect(() => {
    if (!isLoading && isAuthenticated && !token) {
      console.log('🔄 已认证且无验证token，跳转到主页');
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router, token]);

  // Get email from URL if available
  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  useEffect(() => {
    if (!token) {
      setVerificationStatus('error');
      setErrorMessage('Missing verification token');
      return;
    }

    const verifyEmail = async () => {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
        const response = await fetch(`${baseUrl}/users/verify-email`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (data.success) {
          setVerificationStatus('success');
        } else {
          if (data.message.includes('expired')) {
            setVerificationStatus('expired');
          } else {
            setVerificationStatus('error');
            setErrorMessage(data.message || 'Verification failed');
          }
        }
      } catch (error) {
        console.error('Email verification error:', error);
        setVerificationStatus('error');
        setErrorMessage('An error occurred during verification, please try again.');
      }
    };

    verifyEmail();
  }, [token]);

  const handleResendVerification = async () => {
    if (!email && !token) return;

    setIsResending(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${baseUrl}/users/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email || undefined,
          token: token || undefined
        }),
      });

      const data = await response.json();

      if (data.success) {
        setResendSuccess(true);
      } else {
        setErrorMessage(data.message || 'Failed to resend, please try again later.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setErrorMessage('Failed to resend verification email, please check your network connection.');
    } finally {
      setIsResending(false);
    }
  };

  if (isLoading) {
    return (
      <AuthLayout title="Loading..." subtitle="Please wait">
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AuthLayout>
    );
  }

  // Loading state
  if (verificationStatus === 'loading') {
    return (
      <AuthLayout title="Verifying..." subtitle="Verifying your email">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-sm text-gray-600">
            Verifying your email address, please wait...
          </p>
        </div>
      </AuthLayout>
    );
  }

  // Success state
  if (verificationStatus === 'success') {
    return (
      <AuthLayout
        title="Verification Successful"
        subtitle="Your email has been successfully verified"
        showBackButton
        backHref="/auth/login"
      >
        <div className="text-center space-y-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Congratulations! Your email address has been successfully verified.
            </p>
            <p className="text-sm text-gray-600">
              You can now use your account to log in to Newzora.
            </p>
          </div>

          <div className="pt-4">
            <Link
              href="/login"
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Sign In Now
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  // Expired token state
  if (verificationStatus === 'expired') {
    return (
      <AuthLayout
        title="Verification Link Expired"
        subtitle="Please request a new verification email"
        showBackButton
        backHref="/login"
      >
        <div className="text-center space-y-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
            <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              This email verification link has expired. Please request a new verification email.
            </p>
            {email && (
              <p className="text-sm text-gray-500">
                Email address: {email}
              </p>
            )}
          </div>

          {resendSuccess ? (
            <div className="p-4 bg-green-50 border border-green-200 text-green-800 rounded-md text-sm">
              A new verification email has been sent to your email address.
            </div>
          ) : (
            <div className="space-y-3 pt-4">
              <button
                onClick={handleResendVerification}
                disabled={isResending}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending...
                  </>
                ) : (
                  'Resend Verification Email'
                )}
              </button>
            </div>
          )}

          <div className="pt-4 border-t border-gray-200">
            <Link
              href="/login"
              className="text-sm font-medium text-blue-600 hover:text-blue-500"
            >
              Back to Login
            </Link>
          </div>
        </div>
      </AuthLayout>
    );
  }

  // Error state
  return (
    <AuthLayout
      title="Verification Failed"
      subtitle="Problem occurred during email verification"
      showBackButton
      backHref="/auth/login"
    >
      <div className="text-center space-y-4">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {errorMessage || 'Unable to verify your email address. The link may be invalid or expired.'}
          </p>
          {email && (
            <p className="text-sm text-gray-500">
              Email address: {email}
            </p>
          )}
        </div>

        {/* Resend verification section for error state */}
        {(email || token) && (
          <div className="space-y-3 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              Need to resend verification email?
            </p>

            {resendSuccess ? (
              <div className="p-3 bg-green-50 border border-green-200 text-green-800 rounded-md text-sm">
                A new verification email has been sent to your email address.
              </div>
            ) : (
              <button
                onClick={handleResendVerification}
                disabled={isResending}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    Sending...
                  </>
                ) : (
                  'Resend Verification Email'
                )}
              </button>
            )}
          </div>
        )}

        <div className="space-y-3 pt-4">
          <Link
            href="/register"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Register Again
          </Link>
          <Link
            href="/login"
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
}
