'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function DebugNavigationPage() {
  const [navigationLog, setNavigationLog] = useState<string[]>([]);
  const router = useRouter();

  useEffect(() => {
    // 监听所有可能的导航事件
    const originalPushState = window.history.pushState;
    const originalReplaceState = window.history.replaceState;

    // 重写 pushState
    window.history.pushState = function(state, title, url) {
      const logEntry = `${new Date().toLocaleTimeString()} - pushState: ${url}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
      return originalPushState.apply(this, arguments);
    };

    // 重写 replaceState
    window.history.replaceState = function(state, title, url) {
      const logEntry = `${new Date().toLocaleTimeString()} - replaceState: ${url}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
      return originalReplaceState.apply(this, arguments);
    };

    // 监听 popstate 事件
    const handlePopState = (event: PopStateEvent) => {
      const logEntry = `${new Date().toLocaleTimeString()} - popstate: ${window.location.pathname}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
    };

    window.addEventListener('popstate', handlePopState);

    // 监听 beforeunload 事件
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      const logEntry = `${new Date().toLocaleTimeString()} - beforeunload: ${window.location.pathname}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 初始日志
    const initialLog = `${new Date().toLocaleTimeString()} - 页面加载: ${window.location.pathname}`;
    setNavigationLog([initialLog]);

    return () => {
      // 恢复原始方法
      window.history.pushState = originalPushState;
      window.history.replaceState = originalReplaceState;
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const clearLog = () => {
    setNavigationLog([]);
  };

  const testLogin = () => {
    const logEntry = `${new Date().toLocaleTimeString()} - 手动跳转到登录页面`;
    setNavigationLog(prev => [...prev, logEntry]);
    router.push('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">导航调试页面</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">当前页面信息</h2>
          <div className="space-y-2">
            <p><strong>当前路径:</strong> {typeof window !== 'undefined' ? window.location.pathname : '加载中...'}</p>
            <p><strong>完整URL:</strong> {typeof window !== 'undefined' ? window.location.href : '加载中...'}</p>
            <p><strong>时间:</strong> {new Date().toLocaleString()}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">导航日志</h2>
            <div className="space-x-2">
              <button
                onClick={clearLog}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                清除日志
              </button>
              <button
                onClick={testLogin}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                测试跳转到登录页
              </button>
            </div>
          </div>
          
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {navigationLog.length === 0 ? (
              <p className="text-gray-500">暂无导航记录</p>
            ) : (
              <div className="space-y-1">
                {navigationLog.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">测试链接</h2>
          <div className="space-x-4">
            <a href="/login" className="text-blue-600 hover:underline">直接链接到 /login</a>
            <a href="/auth/login" className="text-blue-600 hover:underline">直接链接到 /auth/login</a>
            <a href="/" className="text-blue-600 hover:underline">返回主页</a>
          </div>
        </div>
      </div>
    </div>
  );
}
