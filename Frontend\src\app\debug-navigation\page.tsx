'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter } from 'next/navigation';

function DebugNavigationContent() {
  const [navigationLog, setNavigationLog] = useState<string[]>([]);
  const router = useRouter();

  useEffect(() => {
    // 监听所有可能的导航事件
    const originalPushState = window.history.pushState;
    const originalReplaceState = window.history.replaceState;

    // 重写 pushState
    window.history.pushState = function(state, title, url) {
      const logEntry = `${new Date().toLocaleTimeString()} - pushState: ${url}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
      return originalPushState.apply(this, arguments);
    };

    // 重写 replaceState
    window.history.replaceState = function(state, title, url) {
      const logEntry = `${new Date().toLocaleTimeString()} - replaceState: ${url}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
      return originalReplaceState.apply(this, arguments);
    };

    // 监听 popstate 事件
    const handlePopState = (event: PopStateEvent) => {
      const logEntry = `${new Date().toLocaleTimeString()} - popstate: ${window.location.pathname}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
    };

    window.addEventListener('popstate', handlePopState);

    // 监听 beforeunload 事件
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      const logEntry = `${new Date().toLocaleTimeString()} - beforeunload: ${window.location.pathname}`;
      console.log('🔄 Navigation:', logEntry);
      setNavigationLog(prev => [...prev, logEntry]);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 初始日志
    const initialLog = `${new Date().toLocaleTimeString()} - 页面加载: ${window.location.pathname}`;
    setNavigationLog([initialLog]);

    return () => {
      // 恢复原始方法
      window.history.pushState = originalPushState;
      window.history.replaceState = originalReplaceState;
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const clearLog = () => {
    setNavigationLog([]);
  };

  const testLogin = () => {
    const logEntry = `${new Date().toLocaleTimeString()} - 手动跳转到登录页面`;
    setNavigationLog(prev => [...prev, logEntry]);
    router.push('/login');
  };

  const clearAllCache = () => {
    // 清除所有可能的缓存
    localStorage.clear();
    sessionStorage.clear();
    
    // 清除浏览器缓存（通过强制刷新）
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
    
    const logEntry = `${new Date().toLocaleTimeString()} - 清除所有缓存完成`;
    setNavigationLog(prev => [...prev, logEntry]);
    
    // 强制刷新页面
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const forceNavigateToLogin = () => {
    const logEntry = `${new Date().toLocaleTimeString()} - 强制导航到登录页面（清除历史记录）`;
    setNavigationLog(prev => [...prev, logEntry]);
    
    // 使用 replace 而不是 push，清除历史记录
    window.location.replace('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">导航调试页面</h1>
        
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">当前页面信息</h2>
          <div className="space-y-2">
            <p><strong>当前路径:</strong> {typeof window !== 'undefined' ? window.location.pathname : '加载中...'}</p>
            <p><strong>完整URL:</strong> {typeof window !== 'undefined' ? window.location.href : '加载中...'}</p>
            <p><strong>时间:</strong> {new Date().toLocaleString()}</p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">导航日志</h2>
            <div className="space-x-2">
              <button
                onClick={clearLog}
                className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
              >
                清除日志
              </button>
              <button
                onClick={testLogin}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                测试跳转到登录页
              </button>
              <button
                onClick={clearAllCache}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                清除所有缓存
              </button>
              <button
                onClick={forceNavigateToLogin}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                强制跳转登录页
              </button>
            </div>
          </div>
          
          <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
            {navigationLog.length === 0 ? (
              <p className="text-gray-500">暂无导航记录</p>
            ) : (
              <div className="space-y-1">
                {navigationLog.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">测试链接</h2>
          <div className="space-x-4">
            <a href="/login" className="text-blue-600 hover:underline">直接链接到 /login</a>
            <a href="/auth/login" className="text-blue-600 hover:underline">直接链接到 /auth/login</a>
            <a href="/" className="text-blue-600 hover:underline">返回主页</a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function DebugNavigationPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <DebugNavigationContent />
    </Suspense>
  );
}
