# Newzora 项目结构重组计划

## 🎯 目标
- 清理根目录，保持简洁
- 前后端文件正确分类
- 移除无用文件，保留功能完整性
- 优化项目可维护性

## 📁 新的目录结构

```
Newzora/
├── 📁 Backend/                    # 后端应用
│   ├── 📁 config/                 # 后端配置
│   ├── 📁 middleware/             # 中间件
│   ├── 📁 models/                 # 数据模型
│   ├── 📁 routes/                 # 路由
│   ├── 📁 services/               # 业务逻辑
│   ├── 📁 scripts/                # 后端脚本
│   ├── 📁 tests/                  # 后端测试
│   ├── 📁 uploads/                # 文件上传
│   ├── 📁 logs/                   # 日志文件
│   ├── 📄 server.js               # 服务器入口
│   ├── 📄 package.json            # 后端依赖
│   └── 📄 Dockerfile              # 后端容器
│
├── 📁 Frontend/                   # 前端应用
│   ├── 📁 src/                    # 源代码
│   ├── 📁 public/                 # 静态资源
│   ├── 📁 tests/                  # 前端测试
│   ├── 📄 package.json            # 前端依赖
│   ├── 📄 next.config.js          # Next.js配置
│   └── 📄 Dockerfile              # 前端容器
│
├── 📁 deployment/                 # 部署相关
│   ├── 📁 docker/                 # Docker配置
│   ├── 📁 nginx/                  # Nginx配置
│   ├── 📁 scripts/                # 部署脚本
│   └── 📁 environments/           # 环境配置
│
├── 📁 docs/                       # 项目文档
│   ├── 📁 development/            # 开发文档
│   ├── 📁 deployment/             # 部署文档
│   ├── 📁 api/                    # API文档
│   └── 📁 reports/                # 开发报告
│
├── 📁 tools/                      # 开发工具
│   ├── 📁 scripts/                # 工具脚本
│   └── 📁 utilities/              # 实用工具
│
├── 📄 package.json                # 根项目配置
├── 📄 README.md                   # 项目说明
├── 📄 LICENSE                     # 许可证
└── 📄 .gitignore                  # Git忽略文件
```

## 🔄 文件移动计划

### 需要移动的文件

#### 部署相关 → deployment/
- config/ → deployment/docker/
- deploy*.bat → deployment/scripts/
- manage.ps1 → deployment/scripts/
- docker-compose*.yml → deployment/docker/

#### 文档相关 → docs/
- *.md (报告文件) → docs/reports/
- docs/ 内容重新整理

#### 脚本工具 → tools/
- scripts/ → tools/scripts/
- test-*.* → tools/scripts/
- create_test_users.sql → tools/scripts/

#### 测试相关
- tests/ → 分别移动到 Backend/tests/ 和 Frontend/tests/

### 需要删除的文件
- 空目录
- 重复的Docker文件
- 临时测试文件
- 无用的配置文件

## ✅ 保留功能完整性
- 所有package.json路径更新
- Docker配置路径调整
- 脚本引用路径修正
- 环境变量路径更新
