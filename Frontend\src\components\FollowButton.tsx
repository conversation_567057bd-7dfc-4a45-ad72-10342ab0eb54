'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface FollowButtonProps {
  userId: number;
  username: string;
  initialFollowState?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary';
}

export default function FollowButton({
  userId,
  username,
  initialFollowState = false,
  onFollowChange,
  size = 'md',
  variant = 'primary'
}: FollowButtonProps) {
  const { user, token } = useAuth();
  const [isFollowing, setIsFollowing] = useState(initialFollowState);
  const [isLoading, setIsLoading] = useState(false);
  const [followersCount, setFollowersCount] = useState(0);

  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // Variant classes
  const getButtonClasses = () => {
    const baseClasses = `font-medium rounded-lg transition-all duration-200 flex items-center space-x-2 ${sizeClasses[size]}`;
    
    if (isFollowing) {
      return `${baseClasses} bg-gray-100 text-gray-700 hover:bg-red-50 hover:text-red-600 border border-gray-300`;
    }
    
    if (variant === 'primary') {
      return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
    } else {
      return `${baseClasses} bg-white text-blue-600 border border-blue-600 hover:bg-blue-50`;
    }
  };

  useEffect(() => {
    if (user && token && userId) {
      checkFollowStatus();
      fetchFollowersCount();
    }
  }, [user, token, userId]);

  const checkFollowStatus = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/follows/check/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setIsFollowing(data.data.isFollowing);
      }
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  };

  const fetchFollowersCount = async () => {
    try {
      const response = await fetch(`http://localhost:5000/api/follows/followers/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFollowersCount(data.data.followers.length);
      }
    } catch (error) {
      console.error('Error fetching followers count:', error);
    }
  };

  const handleFollowToggle = async () => {
    if (!user || !token) {
      alert('Please login to follow users');
      return;
    }

    if (user.id === userId) {
      alert('You cannot follow yourself');
      return;
    }

    setIsLoading(true);

    try {
      const url = isFollowing 
        ? `http://localhost:5000/api/follows/${userId}`
        : 'http://localhost:5000/api/follows';
      
      const method = isFollowing ? 'DELETE' : 'POST';
      const body = isFollowing ? undefined : JSON.stringify({ followingId: userId });

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body
      });

      if (response.ok) {
        const newFollowState = !isFollowing;
        setIsFollowing(newFollowState);
        setFollowersCount(prev => newFollowState ? prev + 1 : prev - 1);
        
        // Record activity for following
        if (newFollowState) {
          await recordFollowActivity();
        }
        
        onFollowChange?.(newFollowState);
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Failed to update follow status');
      }
    } catch (error) {
      console.error('Error toggling follow:', error);
      alert('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const recordFollowActivity = async () => {
    try {
      await fetch('http://localhost:5000/api/activities/user-follow', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ followedUserId: userId })
      });
    } catch (error) {
      console.error('Error recording follow activity:', error);
    }
  };

  // Don't show follow button for own profile
  if (user && user.id === userId) {
    return null;
  }

  // Don't show if not logged in
  if (!user) {
    return (
      <button
        onClick={() => alert('Please login to follow users')}
        className={getButtonClasses()}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        <span>Follow</span>
      </button>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={handleFollowToggle}
        disabled={isLoading}
        className={`${getButtonClasses()} ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        {isLoading ? (
          <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : isFollowing ? (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Following</span>
          </>
        ) : (
          <>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Follow</span>
          </>
        )}
      </button>
      
      {followersCount > 0 && (
        <span className="text-sm text-gray-500">
          {followersCount} follower{followersCount !== 1 ? 's' : ''}
        </span>
      )}
    </div>
  );
}
