require('dotenv').config({ path: '.env.test' })

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
process.env.DB_NAME = 'newzora_test'
process.env.DB_USER = 'postgres'
process.env.DB_PASSWORD = 'wasd080980!' || ''
process.env.DB_HOST = 'localhost'
process.env.DB_PORT = '5432'

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Global test setup
beforeAll(async () => {
  // Setup test database connection
  const { sequelize } = require('./config/database')
  
  try {
    await sequelize.authenticate()
    console.log('Test database connection established successfully.')
    
    // Sync database for tests (create tables)
    await sequelize.sync({ force: true })
    console.log('Test database synchronized.')
  } catch (error) {
    console.error('Unable to connect to test database:', error)
  }
})

// Cleanup after all tests
afterAll(async () => {
  const { sequelize } = require('./config/database')
  
  try {
    await sequelize.close()
    console.log('Test database connection closed.')
  } catch (error) {
    console.error('Error closing test database connection:', error)
  }
})

// Reset database before each test
beforeEach(async () => {
  const { sequelize } = require('./config/database')
  
  try {
    // Clear all tables
    await sequelize.truncate({ cascade: true, restartIdentity: true })
  } catch (error) {
    console.error('Error clearing test database:', error)
  }
})
